'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

export default function DesignDemo() {
  return (
    <div className="space-y-12 pb-10">
      <div className="netcare-hero-section py-10 mb-12 -mx-4 sm:-mx-6 lg:-mx-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-3xl font-bold text-[#008b85] mb-3">Netcare Claims Portal</h1>
          <p className="text-gray-700 text-lg font-medium">Providing YOU with the best and safest care</p>
        </div>
      </div>

      {/* Hero Section with Cards */}
      <section className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        {/* Left Card - Primary */}
        <div className="netcare-card-primary p-8 flex flex-col justify-center min-h-[300px]">
          <h2 className="text-2xl font-bold mb-4">Need more ways to access quality care?</h2>
          <p className="text-white font-medium mb-6">
            Get world-class healthcare with our comprehensive services and network of specialists.
          </p>
          <div>
            <button className="bg-white text-[#008b85] px-6 py-2 rounded-sm hover:bg-gray-50 transition-all font-medium">
              Learn more
            </button>
          </div>
        </div>

        {/* Right Card - White with Content */}
        <div className="netcare-card-white p-8 flex flex-col justify-between min-h-[300px]">
          <div>
            <h2 className="text-2xl font-bold text-[#0072bc] mb-4">Prepaid Healthcare Vouchers</h2>
            <p className="text-gray-700 font-medium mb-6">
              Get the daily care you need with access to Netcare's world-class doctors, dentists and optometrists.
            </p>
            <div className="mb-6">
              <button className="bg-[#008b85] text-white px-6 py-2 rounded-sm hover:bg-[#007b76] transition-all font-medium">
                Learn more
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Card Styles Showcase */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-[#0072bc] mb-6 drop-shadow-sm">Professional Card Styles</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Card Style 1 */}
          <div className="netcare-card-primary p-6 rounded-sm" style={{ background: 'linear-gradient(135deg, #00d8cc 0%, #00a19a 100%)' }}>
            <h3 className="text-xl font-semibold mb-3">Teal Card</h3>
            <p className="text-white font-medium mb-4">
              Netcare's primary teal color for important information and primary actions.
            </p>
            <button className="bg-white text-[#008b85] px-4 py-1.5 rounded-sm text-sm font-medium">
              Action
            </button>
          </div>

          {/* Card Style 2 */}
          <div className="netcare-card-secondary p-6 rounded-sm" style={{ background: 'linear-gradient(135deg, #00aaff 0%, #0088cc 100%)' }}>
            <h3 className="text-xl font-semibold mb-3">Blue Card</h3>
            <p className="text-white font-medium mb-4">
              Netcare's blue color for secondary content and supporting information.
            </p>
            <button className="bg-white text-[#0072bc] px-4 py-1.5 rounded-sm text-sm font-medium">
              Action
            </button>
          </div>

          {/* Card Style 3 */}
          <div className="netcare-card-glass p-6 rounded-sm">
            <h3 className="text-xl font-semibold text-[#0072bc] mb-3">Glass Card</h3>
            <p className="text-gray-700 font-medium mb-4">
              Subtle glass effect with clean borders matching Netcare's website style.
            </p>
            <button className="bg-[#008b85] text-white px-4 py-1.5 rounded-sm text-sm font-medium">
              Action
            </button>
          </div>
        </div>
      </section>

      {/* Additional Card Styles */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-[#0072bc] mb-6 drop-shadow-sm">Additional Card Styles</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* White Card */}
          <div className="netcare-card-white p-6 rounded-sm">
            <h3 className="text-xl font-semibold mb-3 text-[#0072bc]">White Card</h3>
            <p className="text-gray-700 font-medium mb-4">
              Clean white background with subtle shadow for general content display.
            </p>
            <button className="bg-[#008b85] text-white px-4 py-1.5 rounded-sm text-sm font-medium">
              Action
            </button>
          </div>

          {/* Accent Card */}
          <div className="netcare-card-accent p-6 rounded-sm">
            <h3 className="text-xl font-semibold mb-3 text-[#0072bc]">Accent Card</h3>
            <p className="text-gray-700 font-medium mb-4">
              White card with teal accent border for important notices or featured content.
            </p>
            <button className="bg-[#008b85] text-white px-4 py-1.5 rounded-sm text-sm font-medium">
              Action
            </button>
          </div>
        </div>
      </section>
      
      {/* Content Sections */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold text-[#0072bc] mb-6 drop-shadow-sm">Professional Content Sections</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Hero Section */}
          <div className="netcare-hero-section p-6 rounded-sm min-h-[200px] flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-xl font-semibold mb-2 text-[#008b85]">Hero Section</h3>
              <p className="text-gray-700 font-medium">Light blue background with subtle border matching Netcare's website</p>
            </div>
          </div>

          {/* Professional Section */}
          <div className="netcare-section-professional bg-white p-6 rounded-sm min-h-[200px] flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-xl font-semibold mb-2 text-[#0072bc]">Professional Section</h3>
              <p className="text-gray-700 font-medium">Clean white background matching Netcare's minimalist style</p>
            </div>
          </div>
        </div>
      </section>

      {/* Design System Benefits */}
      <section className="mb-12 netcare-card-white p-8 rounded-sm">
        <h2 className="text-2xl font-bold text-[#0072bc] mb-6 drop-shadow-sm">Benefits of Our Netcare Design System</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4">
            <h3 className="text-lg font-semibold text-[#008b85] mb-2">Brand Authenticity</h3>
            <p className="text-gray-700 font-medium">Perfectly aligned with Netcare's official brand identity and website aesthetics</p>
          </div>
          <div className="p-4">
            <h3 className="text-lg font-semibold text-[#008b85] mb-2">Accessibility</h3>
            <p className="text-gray-700 font-medium">High contrast text and clean visual hierarchy for improved readability</p>
          </div>
          <div className="p-4">
            <h3 className="text-lg font-semibold text-[#008b85] mb-2">Minimalist Design</h3>
            <p className="text-gray-700 font-medium">Clean, uncluttered interface that focuses on content and user experience</p>
          </div>
        </div>
      </section>

      {/* Back to Home */}
      <div className="text-center mt-12">
        <Link href="/" className="text-white bg-[#008b85] hover:bg-[#007b76] no-underline py-2 px-6 rounded-sm transition-all font-medium">
          Back to Home
        </Link>
      </div>
    </div>
  );
}
