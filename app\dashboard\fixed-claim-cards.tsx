// This file contains only the fixed claim card section for the dashboard
// To be used as a reference for updating the main dashboard page

// Claims Grid with Fixed Text Layout
const FixedClaimsGrid = () => {
  return (
    <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-4">
      {filteredClaims.map((claim, index) => (
        <Card
          key={claim.id}
          className="bg-gray-500 text-white transition-all duration-500 hover:scale-[1.01] group animate-fade-in cursor-pointer shadow-md hover:shadow-lg"
          style={{ animationDelay: `${index * 0.1}s` }}
          onClick={() => handleClaimClick(claim.id)}
        >
          <CardHeader className="pb-4">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300">
                  {getDocumentIcon(claim.type)}
                </div>
                <div>
                  <CardTitle className="text-xl font-bold text-white group-hover:text-white transition-colors duration-300">
                    {claim.id}
                  </CardTitle>
                  <p className="text-sm text-white/90 font-medium">{claim.type}</p>
                </div>
              </div>
              <div className={`inline-flex items-center rounded-full px-3 py-1.5 text-xs font-semibold ${getStatusColor(claim.status)} shadow-md`}>
                {getStatusIcon(claim.status)}
                <span className="ml-2">{claim.status}</span>
              </div>
            </div>
          </CardHeader>
          
          {/* Fixed Card Content - removed height constraint and flex centering */}
          <CardContent className="p-4 pt-0">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <p className="text-white/70 font-medium flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  Submitted
                </p>
                <p className="font-semibold text-white">{claim.submittedDate}</p>
              </div>

              {claim.policyNumber && (
                <div className="space-y-1">
                  <p className="text-white/70 font-medium">Policy Number</p>
                  <p className="font-semibold text-white">{claim.policyNumber}</p>
                </div>
              )}

              {claim.provider && (
                <div className="col-span-2 space-y-1">
                  <p className="text-white/70 font-medium">Provider</p>
                  <p className="font-semibold text-white">{claim.provider}</p>
                </div>
              )}

              {claim.serviceDate && (
                <div className="space-y-1">
                  <p className="text-white/70 font-medium">Service Date</p>
                  <p className="font-semibold text-white">{claim.serviceDate}</p>
                </div>
              )}

              {claim.shortfall && (
                <div className="space-y-1">
                  <p className="text-white/70 font-medium">Shortfall</p>
                  <p className="font-semibold text-white">{claim.shortfall}</p>
                </div>
              )}
            </div>
          </CardContent>

          <CardFooter className="p-4 pt-0 flex justify-end">
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-white/20 hover:text-white"
            >
              View Details <ChevronRight className="ml-1 w-4 h-4" />
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

// Export for reference
export default FixedClaimsGrid;
