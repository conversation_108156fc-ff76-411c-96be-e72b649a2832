'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Upload, FileText, X } from 'lucide-react';

const uploadFormSchema = z.object({
  claimType: z.string().min(1, 'Please select a claim type'),
  policyNumber: z.string().min(1, 'Policy number is required'),
  patientName: z.string().min(1, 'Patient name is required'),
  serviceDate: z.string().min(1, 'Service date is required'),
  description: z.string().optional(),
  files: z.array(z.any()).min(1, 'At least one file is required'),
});

type UploadFormData = z.infer<typeof uploadFormSchema>;

interface UploadFormProps {
  onSubmit?: (data: UploadFormData) => void;
}

export default function UploadForm({ onSubmit }: UploadFormProps) {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  
  const form = useForm<UploadFormData>({
    resolver: zodResolver(uploadFormSchema),
    defaultValues: {
      claimType: '',
      policyNumber: '',
      patientName: '',
      serviceDate: '',
      description: '',
      files: [],
    },
  });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setUploadedFiles(prev => [...prev, ...files]);
    form.setValue('files', [...uploadedFiles, ...files]);
  };

  const removeFile = (index: number) => {
    const newFiles = uploadedFiles.filter((_, i) => i !== index);
    setUploadedFiles(newFiles);
    form.setValue('files', newFiles);
  };

  const handleSubmit = (data: UploadFormData) => {
    console.log('Upload form submitted:', data);
    onSubmit?.(data);
  };

  return (
    <Card className="netcare-card">
      <CardHeader>
        <CardTitle className="text-netcare-white text-xl">
          Upload Documents
        </CardTitle>
        <p className="text-netcare-white/70">
          Upload your medical aid statements and supporting documents
        </p>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="claimType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-netcare-white">Claim Type</FormLabel>
                  <FormControl>
                    <select 
                      {...field}
                      className="w-full p-3 bg-netcare-white/10 border border-netcare-white/20 rounded-lg text-netcare-white focus:border-netcare-cyan focus:outline-none"
                    >
                      <option value="">Select claim type</option>
                      <option value="medical-aid">Medical Aid Statement</option>
                      <option value="provider">Provider Document</option>
                    </select>
                  </FormControl>
                  <FormMessage className="text-red-400" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="policyNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-netcare-white">Policy Number</FormLabel>
                  <FormControl>
                    <Input 
                      {...field}
                      placeholder="Enter your policy number"
                      className="bg-netcare-white/10 border-netcare-white/20 text-netcare-white placeholder:text-netcare-white/50 focus:border-netcare-cyan"
                    />
                  </FormControl>
                  <FormMessage className="text-red-400" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="patientName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-netcare-white">Patient Name</FormLabel>
                  <FormControl>
                    <Input 
                      {...field}
                      placeholder="Enter patient name"
                      className="bg-netcare-white/10 border-netcare-white/20 text-netcare-white placeholder:text-netcare-white/50 focus:border-netcare-cyan"
                    />
                  </FormControl>
                  <FormMessage className="text-red-400" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="serviceDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-netcare-white">Service Date</FormLabel>
                  <FormControl>
                    <Input 
                      {...field}
                      type="date"
                      className="bg-netcare-white/10 border-netcare-white/20 text-netcare-white focus:border-netcare-cyan"
                    />
                  </FormControl>
                  <FormMessage className="text-red-400" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-netcare-white">Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea 
                      {...field}
                      placeholder="Add any additional information about your claim"
                      className="bg-netcare-white/10 border-netcare-white/20 text-netcare-white placeholder:text-netcare-white/50 focus:border-netcare-cyan"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage className="text-red-400" />
                </FormItem>
              )}
            />

            {/* File Upload Section */}
            <div className="space-y-4">
              <Label className="text-netcare-white">Upload Documents</Label>
              
              <div className="border-2 border-dashed border-netcare-white/20 rounded-lg p-6 text-center hover:border-netcare-cyan/50 transition-colors">
                <Upload className="w-12 h-12 text-netcare-white/50 mx-auto mb-4" />
                <p className="text-netcare-white/70 mb-2">
                  Drag and drop your files here, or click to browse
                </p>
                <Input
                  type="file"
                  multiple
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="file-upload"
                />
                <Label 
                  htmlFor="file-upload"
                  className="inline-flex items-center px-4 py-2 bg-netcare-cyan/20 text-netcare-cyan rounded-lg cursor-pointer hover:bg-netcare-cyan/30 transition-colors"
                >
                  Choose Files
                </Label>
              </div>

              {/* Uploaded Files List */}
              {uploadedFiles.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-netcare-white">Uploaded Files:</Label>
                  {uploadedFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-netcare-white/10 rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="w-5 h-5 text-netcare-cyan" />
                        <span className="text-netcare-white text-sm">{file.name}</span>
                        <span className="text-netcare-white/50 text-xs">
                          ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                        className="text-red-400 hover:text-red-300 hover:bg-red-400/10"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex justify-end pt-4">
              <Button 
                type="submit" 
                className="netcare-button"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? 'Uploading...' : 'Submit Claim'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
