'use client';

import { useEffect } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  bundleSize?: number;
  componentName: string;
}

interface PerformanceMonitorProps {
  componentName: string;
  onMetrics?: (metrics: PerformanceMetrics) => void;
  children: React.ReactNode;
}

export default function PerformanceMonitor({ 
  componentName, 
  onMetrics, 
  children 
}: PerformanceMonitorProps) {
  useEffect(() => {
    const startTime = performance.now();
    
    // Measure component load time
    const measureLoadTime = () => {
      const loadTime = performance.now() - startTime;
      
      // Get navigation timing for more detailed metrics
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const renderTime = navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0;
      
      const metrics: PerformanceMetrics = {
        loadTime,
        renderTime,
        componentName,
      };
      
      // Log metrics in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`Performance Metrics for ${componentName}:`, {
          loadTime: `${loadTime.toFixed(2)}ms`,
          renderTime: `${renderTime.toFixed(2)}ms`,
        });
      }
      
      onMetrics?.(metrics);
    };

    // Measure after component is fully rendered
    const timeoutId = setTimeout(measureLoadTime, 0);
    
    return () => clearTimeout(timeoutId);
  }, [componentName, onMetrics]);

  return <>{children}</>;
}

// Hook for measuring component performance
export function usePerformanceMetrics(componentName: string) {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render time: ${duration.toFixed(2)}ms`);
      }
    };
  }, [componentName]);
}

// Utility to measure bundle size impact
export function measureBundleImpact(componentName: string) {
  if (typeof window !== 'undefined' && 'performance' in window) {
    const entries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    const jsEntries = entries.filter(entry => 
      entry.name.includes('.js') && 
      entry.name.includes('chunks')
    );
    
    const totalSize = jsEntries.reduce((sum, entry) => {
      return sum + (entry.transferSize || 0);
    }, 0);
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`Bundle impact for ${componentName}:`, {
        totalJSSize: `${(totalSize / 1024).toFixed(2)}KB`,
        chunkCount: jsEntries.length,
      });
    }
    
    return {
      totalSize,
      chunkCount: jsEntries.length,
    };
  }
  
  return null;
}

// Component to display performance metrics in development
export function PerformanceDebugger() {
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;
    
    const logPerformanceMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        console.group('🚀 Performance Metrics');
        console.log('DNS Lookup:', `${(navigation.domainLookupEnd - navigation.domainLookupStart).toFixed(2)}ms`);
        console.log('TCP Connection:', `${(navigation.connectEnd - navigation.connectStart).toFixed(2)}ms`);
        console.log('Request/Response:', `${(navigation.responseEnd - navigation.requestStart).toFixed(2)}ms`);
        console.log('DOM Processing:', `${(navigation.domComplete - navigation.domContentLoadedEventStart).toFixed(2)}ms`);
        console.log('Load Event:', `${(navigation.loadEventEnd - navigation.loadEventStart).toFixed(2)}ms`);
        console.log('Total Load Time:', `${(navigation.loadEventEnd - navigation.fetchStart).toFixed(2)}ms`);
        console.groupEnd();
      }
      
      // Log resource timing
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const jsResources = resources.filter(r => r.name.includes('.js'));
      const cssResources = resources.filter(r => r.name.includes('.css'));
      
      console.group('📦 Resource Loading');
      console.log('JavaScript files:', jsResources.length);
      console.log('CSS files:', cssResources.length);
      console.log('Total JS size:', `${jsResources.reduce((sum, r) => sum + (r.transferSize || 0), 0) / 1024}KB`);
      console.log('Total CSS size:', `${cssResources.reduce((sum, r) => sum + (r.transferSize || 0), 0) / 1024}KB`);
      console.groupEnd();
    };
    
    // Log metrics after page load
    if (document.readyState === 'complete') {
      logPerformanceMetrics();
    } else {
      window.addEventListener('load', logPerformanceMetrics);
      return () => window.removeEventListener('load', logPerformanceMetrics);
    }
  }, []);
  
  return null;
}
