import React from 'react';
import Link from 'next/link';
import {
  LayoutGrid, // For My Claims (can be more specific like ListChecks)
  PlusCircle, // For New Claim
  LifeBuoy,   // For Support
  UserCircle, // For User Avatar placeholder
  Moon,       // For Theme Toggle (can be Sun too)
  LogOut,     // For Logout
  ShieldCheck // Placeholder for Netcare Plus Logo icon
} from 'lucide-react';

const Header: React.FC = () => {
  return (
    <header className="bg-netcare-navy text-netcare-text-white shadow-lg">
      <div className="container mx-auto flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
        {/* Logo and Brand Name */}
        <Link href="/" className="flex items-center">
          <ShieldCheck className="h-8 w-8 text-netcare-cyan mr-2" /> 
          <span className="font-bold text-xl tracking-tight">NETCARE</span>
          <span className="font-semibold text-xl text-netcare-light-cyan ml-1">plus</span>
        </Link>

        {/* Navigation Links */}
        <nav className="hidden md:flex items-center space-x-6">
          <Link href="/dashboard" className="flex items-center text-netcare-light-gray hover:text-netcare-cyan transition-colors">
            <LayoutGrid size={18} className="mr-2" />
            My Claims
          </Link>
          <Link href="/new-claim" className="flex items-center text-netcare-light-gray hover:text-netcare-cyan transition-colors">
            <PlusCircle size={18} className="mr-2" />
            New Claim
          </Link>
          <Link href="/support" className="flex items-center text-netcare-light-gray hover:text-netcare-cyan transition-colors">
            <LifeBuoy size={18} className="mr-2" />
            Support
          </Link>
        </nav>

        {/* User Actions */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <UserCircle size={24} className="text-netcare-light-cyan mr-2" />
            <span className="text-sm font-medium text-netcare-light-gray hidden sm:inline">S.Jajivan</span> {/* Placeholder Name */}
          </div>
          <button aria-label="Toggle theme" className="text-netcare-light-gray hover:text-netcare-cyan transition-colors">
            <Moon size={20} />
          </button>
          <button aria-label="Logout" className="text-netcare-light-gray hover:text-netcare-cyan transition-colors">
            <LogOut size={20} />
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
