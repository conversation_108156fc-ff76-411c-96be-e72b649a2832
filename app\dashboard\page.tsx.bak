'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { 
  FileText, 
  Plus, 
  Filter, 
  User, 
  LogOut, 
  HelpCircle,
  ChevronRight,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  FileCheck,
  Search,
  TrendingUp,
  Calendar,
  DollarSign,
  Activity,
  Pie<PERSON>hart as PieChartIcon,
  BarChart as BarChartIcon,
  LineChart as LineChartIcon,
  AreaChart as AreaChartIcon
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { 
  ChartContainer, 
  ChartTooltip, 
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent 
} from '@/components/ui/chart';
import {
  <PERSON><PERSON>hart,
  Pie,
  LineChart,
  Line,
  BarChart,
  Bar,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';

interface Claim {
  id: string;
  type: 'Medical Aid Statement' | 'Provider Document';
  status: 'Submitted' | 'Processing' | 'Processed' | 'Completed' | 'Rejected' | 'Requires Attention';
  submittedDate: string;
  policyNumber?: string;
  provider?: string;
  serviceDate?: string;
  amountPaid: string;
  amountUnpaid: string;
  shortfall?: string;
}

export default function DashboardPage() {
  const [filterStatus, setFilterStatus] = useState('All Claims');
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();
  
  // Chart data will be defined later

  const claims: Claim[] = [
    {
      id: 'claim-1',
      type: 'Medical Aid Statement',
      status: 'Submitted',
      submittedDate: 'May 28, 2025',
      policyNumber: 'POL-100000',
      amountPaid: 'R750.00',
      amountUnpaid: 'R250.00'
    },
    {
      id: 'claim-2',
      type: 'Provider Document',
      status: 'Processing',
      submittedDate: 'May 25, 2025',
      provider: 'Dr. Smith Medical Practice',
      serviceDate: 'May 18, 2025',
      shortfall: 'R500.00',
      amountPaid: 'R0.00',
      amountUnpaid: 'R500.00'
    },
    {
      id: 'claim-3',
      type: 'Medical Aid Statement',
      status: 'Requires Attention',
      submittedDate: 'May 22, 2025',
      policyNumber: 'POL-100002',
      amountPaid: 'R750.00',
      amountUnpaid: 'R250.00'
    },
    {
      id: 'claim-4',
      type: 'Provider Document',
      status: 'Processed',
      submittedDate: 'May 19, 2025',
      provider: 'Dr. Smith Medical Practice',
      serviceDate: 'May 12, 2025',
      shortfall: 'R500.00',
      amountPaid: 'R0.00',
      amountUnpaid: 'R500.00'
    },
    {
      id: 'claim-5',
      type: 'Medical Aid Statement',
      status: 'Completed',
      submittedDate: 'May 16, 2025',
      policyNumber: 'POL-100004',
      amountPaid: 'R750.00',
      amountUnpaid: 'R250.00'
    },
    {
      id: 'claim-6',
      type: 'Provider Document',
      status: 'Rejected',
      submittedDate: 'May 13, 2025',
      provider: 'Dr. Smith Medical Practice',
      serviceDate: 'May 6, 2025',
      shortfall: 'R500.00',
      amountPaid: 'R0.00',
      amountUnpaid: 'R500.00'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Submitted':
        return <Clock className="w-4 h-4" />;
      case 'Processing':
        return <Activity className="w-4 h-4" />;
      case 'Processed':
        return <CheckCircle className="w-4 h-4" />;
      case 'Completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'Rejected':
        return <XCircle className="w-4 h-4" />;
      case 'Requires Attention':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    // Enhanced color scheme with better contrast
    switch (status) {
      case 'Submitted':
        return 'bg-netcare-cyan/20 text-netcare-cyan border border-netcare-cyan/30';
      case 'Processing':
        return 'bg-netcare-light-cyan/20 text-netcare-light-cyan border border-netcare-light-cyan/30';
      case 'Processed':
        return 'bg-netcare-cyan/20 text-netcare-cyan border border-netcare-cyan/30';
      case 'Completed':
        return 'bg-netcare-light-cyan/20 text-netcare-light-cyan border border-netcare-light-cyan/30';
      case 'Rejected':
        return 'bg-netcare-silver/20 text-netcare-silver border border-netcare-silver/30';
      case 'Requires Attention':
        return 'bg-netcare-pale-cyan/20 text-netcare-pale-cyan border border-netcare-pale-cyan/30';
      default:
        return 'bg-netcare-silver/20 text-netcare-silver border border-netcare-silver/30';
    }
  };

  const getDocumentIcon = (type: string) => {
    return type === 'Medical Aid Statement' ?
      <FileText className="w-5 h-5 text-netcare-cyan" /> :
      <FileCheck className="w-5 h-5 text-netcare-cyan" />;
  };

  const filteredClaims = claims.filter(claim => {
    const matchesFilter = filterStatus === 'All Claims' || claim.status === filterStatus;
    const matchesSearch = searchQuery === '' || 
      claim.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      claim.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      claim.provider?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      claim.policyNumber?.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  // Calculate summary stats
  const totalClaims = claims.length;
  const completedClaims = claims.filter(c => c.status === 'Completed').length;
  const pendingClaims = claims.filter(c => ['Submitted', 'Processing'].includes(c.status)).length;
  const totalPaid = claims.reduce((sum, claim) => {
    const amount = parseFloat(claim.amountPaid.replace('R', '').replace(',', ''));
    return sum + (isNaN(amount) ? 0 : amount);
  }, 0);

  // Prepare data for charts
  // 1. Claims Status Distribution Chart
  const statusCounts = claims.reduce((acc, claim) => {
    acc[claim.status] = (acc[claim.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const statusDistributionData = Object.entries(statusCounts).map(([name, value]) => ({ name, value }));

  // Status colors for pie chart
  const statusColors = {
    'Submitted': '#38bdf8', // netcare-cyan
    'Processing': '#7dd3fc', // netcare-light-cyan
    'Processed': '#0ea5e9', // netcare-cyan variant
    'Completed': '#0284c7', // netcare-light-cyan variant
    'Rejected': '#94a3b8', // netcare-silver
    'Requires Attention': '#bae6fd', // netcare-pale-cyan
  };

  // 2. Claims Timeline Chart - Mock data (would be replaced with real data)
  const timelineData = [
    { month: 'Jan', claims: 4 },
    { month: 'Feb', claims: 6 },
    { month: 'Mar', claims: 8 },
    { month: 'Apr', claims: 5 },
    { month: 'May', claims: 12 },
    { month: 'Jun', claims: 6 },
  ];

  // 3. Payment Analysis Chart
  const paymentData = claims.map(claim => {
    const paid = parseFloat(claim.amountPaid.replace('R', '').replace(',', ''));
    const unpaid = parseFloat(claim.amountUnpaid.replace('R', '').replace(',', ''));
    return {
      name: claim.id,
      paid: isNaN(paid) ? 0 : paid,
      unpaid: isNaN(unpaid) ? 0 : unpaid
    };
  });

  // 4. Monthly Claims Activity Chart - Mock data (would be replaced with real data)
  const monthlyActivityData = [
    { month: 'Jan', submitted: 3, completed: 2 },
    { month: 'Feb', submitted: 5, completed: 3 },
    { month: 'Mar', submitted: 7, completed: 4 },
    { month: 'Apr', submitted: 4, completed: 6 },
    { month: 'May', submitted: 10, completed: 7 },
    { month: 'Jun', submitted: 6, completed: 5 },
  ];

  const handleClaimClick = (claimId: string) => {
    router.push(`/claim/${claimId}`);
  };

  return (
    <div className="min-h-screen">

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-6 bg-[#f0f9f8]">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-6 gap-4">
          <div>
            <h2 className="text-4xl font-bold text-gray-800 mb-3 tracking-tight">Your Claims</h2>
            <p className="text-gray-600 text-lg">Manage and track your medical claims</p>
          </div>
          <Link href="/new-claim">
            <Button className="bg-teal-500 hover:bg-teal-600 text-white font-semibold px-4 py-2 rounded-lg flex items-center gap-2 shadow-md hover:shadow-lg transition-all duration-300">
              <Plus className="w-5 h-5" />
              Start a New Claim
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="netcare-card group transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-netcare-white/90 text-sm font-medium mb-1">Total Claims</p>
                  <p className="text-3xl font-bold text-netcare-white">{totalClaims}</p>
                </div>
                <div className="bg-white/20 p-3 rounded-full group-hover:bg-netcare-cyan/20 transition-colors duration-300">
                  <FileText className="w-6 h-6 text-netcare-cyan" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="netcare-card group transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-netcare-white/90 text-sm font-medium mb-1">Completed</p>
                  <p className="text-3xl font-bold text-netcare-white">{completedClaims}</p>
                </div>
                <div className="bg-white/20 p-3 rounded-full group-hover:bg-netcare-cyan/20 transition-colors duration-300">
                  <CheckCircle className="w-6 h-6 text-netcare-cyan" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="netcare-card group transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-netcare-white/90 text-sm font-medium mb-1">Pending</p>
                  <p className="text-3xl font-bold text-netcare-white">{pendingClaims}</p>
                </div>
                <div className="bg-white/20 p-3 rounded-full group-hover:bg-netcare-cyan/20 transition-colors duration-300">
                  <Clock className="w-6 h-6 text-netcare-cyan" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="netcare-card group transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-netcare-white/90 text-sm font-medium mb-1">Total Paid</p>
                  <p className="text-3xl font-bold text-netcare-cyan">R{totalPaid.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}</p>
                </div>
                <div className="bg-white/20 p-3 rounded-full group-hover:bg-netcare-cyan/20 transition-colors duration-300">
                  <DollarSign className="w-6 h-6 text-netcare-cyan" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="mb-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-4">Claims Analytics</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Claims Status Distribution Chart */}
            <Card className="bg-gray-500 group transition-all duration-500 shadow-md">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-medium text-white flex items-center gap-2">
                    <PieChartIcon className="w-4 h-4 text-white" />
                    Claims Status Distribution
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-0 h-[180px] flex items-center justify-center">
                {/* Chart content will be rendered here */}
              </CardContent>
            </Card>

            {/* Claims Timeline Chart */}
            <Card className="bg-gray-500 group transition-all duration-500 shadow-md">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-medium text-white flex items-center gap-2">
                    <LineChartIcon className="w-4 h-4 text-white" />
                    Claims Timeline
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-0 h-[180px] flex items-center justify-center">
                {/* Chart content will be rendered here */}
              </CardContent>
            </Card>

            {/* Payment Analysis Chart */}
            <Card className="bg-gray-500 group transition-all duration-500 shadow-md">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-medium text-white flex items-center gap-2">
                    <BarChartIcon className="w-4 h-4 text-white" />
                    Payment Analysis
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-0 h-[180px] flex items-center justify-center">
                {/* Chart content will be rendered here */}
              </CardContent>
            </Card>

            {/* Monthly Claims Activity Chart */}
            <Card className="bg-gray-500 group transition-all duration-500 shadow-md">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-medium text-white flex items-center gap-2">
                    <AreaChartIcon className="w-4 h-4 text-white" />
                    Monthly Claims Activity
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-0 h-[180px] flex items-center justify-center">
                {/* Chart content will be rendered here */}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Search and Filter Section */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5" />
            <Input
              type="text"
              placeholder="Search claims by ID, type, provider, or policy number..."
              className="pl-10 bg-white border-gray-300 text-gray-800 placeholder:text-gray-500 focus:border-teal-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Select
            value={filterStatus}
            onValueChange={setFilterStatus}
          >
            <SelectTrigger className="bg-white border-gray-300 text-gray-800 w-[180px]">
              <SelectValue placeholder="All Claims" />
            </SelectTrigger>
            <SelectContent className="bg-white border-gray-300 text-gray-800">
              <SelectItem value="all">All Claims</SelectItem>
              <SelectItem value="submitted">Submitted</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Claims Grid */}
        <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-4">
          {filteredClaims.map((claim, index) => (
            <Card
              key={claim.id}
              className="bg-gray-500 text-white transition-all duration-500 hover:scale-[1.01] group animate-fade-in cursor-pointer shadow-md hover:shadow-lg"
              style={{ animationDelay: `${index * 0.1}s` }}
              onClick={() => handleClaimClick(claim.id)}
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300">
                      {getDocumentIcon(claim.type)}
                    </div>
                    <div>
                      <CardTitle className="text-xl font-bold text-white group-hover:text-white transition-colors duration-300">
                        {claim.id}
                      </CardTitle>
                      <p className="text-sm text-white/90 font-medium">{claim.type}</p>
                    </div>
                  </div>
                  <div className={`inline-flex items-center rounded-full px-3 py-1.5 text-xs font-semibold ${getStatusColor(claim.status)} shadow-md`}>
                    {getStatusIcon(claim.status)}
                    <span className="ml-2">{claim.status}</span>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="p-4 pt-0 h-[180px] flex items-center justify-center">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="space-y-1">
                    <p className="text-netcare-white/70 font-medium flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      Submitted
                    </p>
                    <p className="font-semibold text-netcare-white">{claim.submittedDate}</p>
                  </div>

                  {claim.policyNumber && (
                    <div className="space-y-1">
                      <p className="text-netcare-white/90 font-medium">Policy Number</p>
                      <p className="font-semibold text-netcare-white">{claim.policyNumber}</p>
                    </div>
                  )}

                  {claim.provider && (
                    <div className="col-span-2 space-y-1">
                      <p className="text-netcare-white/90 font-medium">Provider</p>
                      <p className="font-semibold text-netcare-white">{claim.provider}</p>
                    </div>
                  )}

                  {claim.serviceDate && (
                    <div className="space-y-1">
                      <p className="text-netcare-white/90 font-medium">Service Date</p>
                      <p className="font-semibold text-netcare-white">{claim.serviceDate}</p>
                    </div>
                  )}

                  {claim.shortfall && (
                    <div className="space-y-1">
                      <p className="text-netcare-white/90 font-medium">Shortfall</p>
                      <p className="font-semibold text-netcare-cyan">{claim.shortfall}</p>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm pt-4 border-t border-netcare-cyan/20">
                  <div className="space-y-1">
                    <p className="text-netcare-white/70 font-medium flex items-center">
                      <TrendingUp className="w-3 h-3 mr-1" />
                      Amount Paid
                    </p>
                    <p className="font-bold text-netcare-cyan text-lg">{claim.amountPaid}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-netcare-white/90 font-medium">Amount Unpaid</p>
                    <p className="font-bold text-netcare-light-cyan text-lg">{claim.amountUnpaid}</p>
                  </div>
                </div>

                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClaimClick(claim.id);
                  }}
                  variant="ghost"
                  className="w-full justify-between text-netcare-cyan hover:text-netcare-white hover:bg-netcare-cyan/20 mt-6 h-12 font-semibold group-hover:bg-netcare-cyan/15 transition-all duration-300"
                >
                  View Details
                  <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredClaims.length === 0 && (
          <div className="text-center py-20">
            <div className="w-24 h-24 bg-netcare-cyan/15 rounded-full flex items-center justify-center mx-auto mb-6">
              <FileText className="w-12 h-12 text-netcare-cyan" />
            </div>
            <h3 className="text-4xl font-bold text-white">6</h3>
            <p className="text-white/80 text-sm font-medium">Total Claims</p>
            <p className="text-white/80 text-lg mb-8 max-w-md mx-auto">
              {searchQuery ? 'No claims match your search criteria.' : 'No claims match the selected filter criteria.'}
            </p>
            {searchQuery && (
              <Button
                variant="outline"
                onClick={() => setSearchQuery('')}
                className="border-netcare-cyan text-netcare-cyan hover:bg-netcare-cyan/20 hover:text-netcare-white transition-all duration-300 font-medium"
              >
                Clear Search
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}