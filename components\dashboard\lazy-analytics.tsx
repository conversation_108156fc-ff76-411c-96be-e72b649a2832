'use client';

import React, { Suspense, lazy } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { 
  <PERSON><PERSON><PERSON> as PieChartIcon,
  BarChart as BarChartIcon,
  <PERSON>Chart as LineChartIcon,
  AreaChart as AreaChartIcon
} from 'lucide-react';

// Lazy load the analytics charts component
const AnalyticsCharts = lazy(() => import('./analytics-charts'));

interface Claim {
  id: string;
  type: 'Medical Aid Statement' | 'Provider Document';
  status: 'Submitted' | 'Processing' | 'Processed' | 'Completed' | 'Rejected' | 'Requires Attention';
  submittedDate: string;
  policyNumber?: string;
  provider?: string;
  serviceDate?: string;
  amountPaid: string;
  amountUnpaid: string;
  shortfall?: string;
}

interface LazyAnalyticsProps {
  claims: Claim[];
}

// Loading skeleton for charts
const ChartSkeleton = () => (
  <div className="mb-6">
    <h3 className="text-2xl font-bold text-gray-800 mb-4">Claims Analytics</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {[1, 2, 3, 4].map((i) => (
        <Card key={i} className="netcare-card group transition-all duration-500">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium text-netcare-white flex items-center gap-2">
                {i === 1 && <PieChartIcon className="w-4 h-4 text-netcare-cyan" />}
                {i === 2 && <LineChartIcon className="w-4 h-4 text-netcare-cyan" />}
                {i === 3 && <BarChartIcon className="w-4 h-4 text-netcare-cyan" />}
                {i === 4 && <AreaChartIcon className="w-4 h-4 text-netcare-cyan" />}
                <div className="h-4 bg-netcare-white/20 rounded animate-pulse w-32"></div>
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 h-[200px] flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-netcare-cyan/20 rounded-full flex items-center justify-center mx-auto mb-3 animate-pulse">
                {i === 1 && <PieChartIcon className="w-8 h-8 text-netcare-cyan" />}
                {i === 2 && <LineChartIcon className="w-8 h-8 text-netcare-cyan" />}
                {i === 3 && <BarChartIcon className="w-8 h-8 text-netcare-cyan" />}
                {i === 4 && <AreaChartIcon className="w-8 h-8 text-netcare-cyan" />}
              </div>
              <div className="h-3 bg-netcare-white/20 rounded animate-pulse w-24 mx-auto"></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

export default function LazyAnalytics({ claims }: LazyAnalyticsProps) {
  return (
    <Suspense fallback={<ChartSkeleton />}>
      <AnalyticsCharts claims={claims} />
    </Suspense>
  );
}
