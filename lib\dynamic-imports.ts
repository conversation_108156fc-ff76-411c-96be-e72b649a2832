import dynamic from 'next/dynamic';
import React, { ComponentType } from 'react';

// Loading component for lazy-loaded components
export const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-netcare-cyan"></div>
  </div>
);

// Dynamic import utilities with consistent loading states
export const createDynamicComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  loadingComponent?: ComponentType
) => {
  return dynamic(importFn, {
    loading: loadingComponent || LoadingSpinner,
    ssr: false, // Disable SSR for better code splitting
  });
};

// Pre-configured dynamic imports for common components
export const DynamicAnalyticsCharts = createDynamicComponent(
  () => import('@/components/dashboard/analytics-charts')
);

export const DynamicUploadForm = createDynamicComponent(
  () => import('@/components/forms/upload-form')
);

export const DynamicClaimForm = createDynamicComponent(
  () => import('@/components/forms/claim-form')
);

export const DynamicContactForm = createDynamicComponent(
  () => import('@/components/forms/contact-form')
);

// Dynamic imports for heavy third-party components
export const DynamicDatePicker = createDynamicComponent(
  () => import('react-day-picker').then(mod => ({ default: mod.DayPicker }))
);

export const DynamicPDFViewer = createDynamicComponent(
  () => import('@/components/pdf/pdf-viewer')
);

// Utility for conditional dynamic loading
export const loadComponentWhen = <T extends ComponentType<any>>(
  condition: boolean,
  importFn: () => Promise<{ default: T }>,
  fallback?: ComponentType
) => {
  if (!condition && fallback) {
    return fallback;
  }
  
  return createDynamicComponent(importFn);
};

// Preload utilities for better UX
export const preloadComponent = (importFn: () => Promise<any>) => {
  // Preload on hover or focus
  const preload = () => {
    importFn().catch(() => {
      // Silently fail if preload fails
    });
  };

  return {
    onMouseEnter: preload,
    onFocus: preload,
  };
};

// Route-based preloading
export const preloadRouteComponent = (route: string) => {
  const importMap: Record<string, () => Promise<any>> = {
    '/dashboard': () => import('@/components/dashboard/analytics-charts'),
    '/upload-documents': () => import('@/components/forms/upload-form'),
    '/new-claim': () => import('@/components/forms/claim-form'),
    '/support': () => import('@/components/forms/contact-form'),
  };

  const importFn = importMap[route];
  if (importFn) {
    return preloadComponent(importFn);
  }

  return {};
};
