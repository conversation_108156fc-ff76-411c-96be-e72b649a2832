'use client';
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft,
  FileText, 
  User, 
  LogOut, 
  HelpCircle,
  Plus,
  Download,
  Eye,
  AlertTriangle,
  Clock,
  RotateCcw,
  Calendar,
  CreditCard,
  Building,
  DollarSign,
  Info,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';
import { ThemeToggle } from '@/components/ui/theme-toggle';

interface ClaimDetailsProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function ClaimDetailsPage({ params }: ClaimDetailsProps) {
  const { id } = await params;
  // Mock data - in real app this would come from API based on params.id
  const claimData = {
    id: 'claim-3',
    title: 'Claim claim-3',
    type: 'Medical Aid Statement',
    status: 'Requires Attention',
    submittedDate: 'May 21, 2025',
    submittedTime: '1:51 PM',
    summary: {
      policyNumber: 'POL-100002',
      patientName: '<PERSON>',
      medicalAidScheme: 'HealthCare Plus',
      amountPaid: 'R750.00',
      amountUnpaid: 'R250.00',
      reasonForNonPayment: 'Exceeds annual limit'
    },
    history: [
      {
        status: 'Submitted',
        date: 'May 21, 2025',
        time: '1:51 PM',
        icon: <Clock className="w-4 h-4" />,
        color: 'text-cyan-400'
      },
      {
        status: 'Processing',
        date: 'May 22, 2025',
        time: '1:51 PM',
        icon: <RotateCcw className="w-4 h-4" />,
        color: 'text-indigo-400'
      },
      {
        status: 'Requires Attention',
        date: 'May 23, 2025',
        time: '1:51 PM',
        icon: <AlertTriangle className="w-4 h-4" />,
        color: 'text-orange-400',
        description: 'Missing information on form'
      }
    ],
    alertMessage: {
      title: 'This claim requires your attention',
      description: 'Please contact support for assistance with this claim.'
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Requires Attention':
        return 'bg-orange-500/20 text-orange-300 border-orange-400/40';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-400/40';
    }
  };

  return (
    <div className="min-h-screen bg-netcare-gradient">
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-10">
        {/* Back Navigation */}
        <div className="mb-8">
          <Link 
            href="/dashboard" 
            className="inline-flex items-center text-netcare-white/70 hover:text-netcare-gold transition-colors group"
          >
            <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
            Back to Claims Summary
          </Link>
        </div>

        {/* Page Header */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-10 gap-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-netcare-gold/20 rounded-xl">
              <FileText className="w-8 h-8 text-cyan-400" />
            </div>
            <div>
              <div className="flex items-center gap-3 mb-2">
                <h2 className="text-3xl font-bold text-netcare-white">{claimData.title}</h2>
                <Badge className={`${getStatusColor(claimData.status)} shadow-lg font-medium px-3 py-1`}>
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  {claimData.status}
                </Badge>
              </div>
              <p className="text-netcare-white/70 text-lg">
                {claimData.type} • Submitted on {claimData.submittedDate}
              </p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Button 
              className="netcare-button-secondary"
            >
              <Eye className="w-4 h-4 mr-2" />
              View
            </Button>
            <Button className="netcare-button">
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Left Column - Claim Summary */}
          <div className="lg:col-span-2 space-y-8">
            {/* Alert Message */}
            <Card className="border-orange-500/30 bg-orange-500/10 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="p-2 bg-orange-500/20 rounded-lg flex-shrink-0">
                    <AlertTriangle className="w-5 h-5 text-orange-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-orange-300 mb-2">
                      {claimData.alertMessage.title}
                    </h3>
                    <p className="text-orange-200/80">
                      {claimData.alertMessage.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Claim Summary */}
            <Card className="netcare-card border-netcare-gold/30">
              <CardHeader>
                <CardTitle className="text-netcare-white text-xl">Claim Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <CreditCard className="w-4 h-4 text-netcare-white/60" />
                        <p className="text-netcare-white/60 text-sm font-medium">Policy Number</p>
                      </div>
                      <p className="text-netcare-white font-semibold text-lg">{claimData.summary.policyNumber}</p>
                    </div>
                    
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Building className="w-4 h-4 text-netcare-white/60" />
                        <p className="text-netcare-white/60 text-sm font-medium">Medical Aid Scheme</p>
                      </div>
                      <p className="text-netcare-white font-semibold text-lg">{claimData.summary.medicalAidScheme}</p>
                    </div>
                    
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Info className="w-4 h-4 text-netcare-white/60" />
                        <p className="text-netcare-white/60 text-sm font-medium">Reasons for Non-Payment</p>
                      </div>
                      <p className="text-netcare-white font-semibold text-lg">{claimData.summary.reasonForNonPayment}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <User className="w-4 h-4 text-netcare-white/60" />
                        <p className="text-netcare-white/60 text-sm font-medium">Patient Name</p>
                      </div>
                      <p className="text-netcare-white font-semibold text-lg">{claimData.summary.patientName}</p>
                    </div>
                    
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <DollarSign className="w-4 h-4 text-netcare-white/60" />
                        <p className="text-netcare-white/60 text-sm font-medium">Amount Paid</p>
                      </div>
                      <p className="text-cyan-400 font-bold text-xl">{claimData.summary.amountPaid}</p>
                    </div>
                    
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <DollarSign className="w-4 h-4 text-netcare-white/60" />
                        <p className="text-netcare-white/60 text-sm font-medium">Amount Unpaid</p>
                      </div>
                      <p className="text-orange-400 font-bold text-xl">{claimData.summary.amountUnpaid}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Claim History */}
          <div className="space-y-8">
            <Card className="netcare-card border-netcare-gold/30">
              <CardHeader>
                <CardTitle className="text-netcare-white text-xl">Claim History</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  {claimData.history.map((item, index) => (
                    <div key={index} className="relative">
                      {index < claimData.history.length - 1 && (
                        <div className="absolute left-6 top-12 w-0.5 h-16 bg-netcare-gold/20"></div>
                      )}
                      <div className="flex items-start gap-4">
                        <div className={`p-3 rounded-full bg-slate-800/50 ${item.color} flex-shrink-0`}>
                          {item.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <div className={`w-3 h-3 rounded-full ${item.color === 'text-cyan-400' ? 'bg-cyan-400' : item.color === 'text-indigo-400' ? 'bg-indigo-400' : 'bg-orange-400'}`}></div>
                            <p className={`font-semibold ${item.color}`}>{item.status}</p>
                          </div>
                          <p className="text-netcare-white/60 text-sm mb-1">
                            {item.date} {item.time}
                          </p>
                          {item.description && (
                            <p className="text-netcare-white/80 text-sm">
                              {item.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Content ends here */}
    </div>
  );
}