import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',

        // Modern gradient system
        'netcare-gradient': 'linear-gradient(135deg, #080F1A 0%, #0F2027 25%, #2C5282 50%, #0F2027 75%, #080F1A 100%)',
        'netcare-hero-gradient': 'linear-gradient(135deg, #080F1A 0%, #1B4B5A 30%, #2C5282 60%, #22D3EE 100%)',
        'netcare-card-gradient': 'linear-gradient(135deg, rgba(15, 32, 39, 0.8) 0%, rgba(27, 75, 90, 0.6) 50%, rgba(15, 32, 39, 0.8) 100%)',
        'netcare-button-gradient': 'linear-gradient(135deg, #0891B2 0%, #0E7490 50%, #06B6D4 100%)',
        'netcare-accent-gradient': 'linear-gradient(135deg, #22D3EE 0%, #06B6D4 50%, #0891B2 100%)',

        // Modern mesh gradients
        'netcare-mesh-gradient': 'radial-gradient(circle at 20% 80%, rgba(34, 211, 238, 0.15) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(125, 211, 252, 0.12) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(44, 82, 130, 0.08) 0%, transparent 50%), linear-gradient(135deg, #080F1A 0%, #0F2027 25%, #2C5282 50%, #0F2027 75%, #080F1A 100%)',

        // Enhanced orb gradients
        'netcare-orb-gradient': 'radial-gradient(circle, rgba(34, 211, 238, 0.08) 0%, rgba(34, 211, 238, 0.04) 40%, transparent 70%)',
        'netcare-orb-secondary': 'radial-gradient(circle, rgba(125, 211, 252, 0.06) 0%, rgba(125, 211, 252, 0.03) 40%, transparent 70%)',
        'netcare-circles': 'radial-gradient(circle at 20% 80%, rgba(34, 211, 238, 0.15) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(125, 211, 252, 0.1) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(34, 211, 238, 0.08) 0%, transparent 50%)',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },

      colors: {
        // Modern Netcare Brand Colors - Enhanced Sleek Theme
        netcare: {
          // Modern primary colors - deeper and more sophisticated
          'deep-teal': '#080F1A',
          'dark-teal': '#0F2027',
          'medium-teal': '#1B4B5A',
          'teal': '#2C5282',
          'light-teal': '#4A90A4',

          // Enhanced cyan palette
          'cyan': '#22D3EE',
          'light-cyan': '#7DD3FC',
          'pale-cyan': '#A5F3FC',
          'bright-cyan': '#06B6D4',

          // Modern neutral colors
          'navy': '#0F172A',
          'slate': '#1E293B',
          'gray': '#475569',
          'light-gray': '#64748B',
          'silver': '#94A3B8',
          'platinum': '#CBD5E1',

          // Modern background system
          'bg-primary': '#080F1A',
          'bg-secondary': '#0F2027',
          'bg-tertiary': '#1B4B5A',
          'bg-light': '#F0FDFF',
          'bg-card': 'rgba(15, 32, 39, 0.8)',
          'bg-card-hover': 'rgba(27, 75, 90, 0.7)',
          'bg-glass': 'rgba(15, 32, 39, 0.6)',

          // Enhanced text colors
          'text-primary': '#FFFFFF',
          'text-secondary': 'rgba(255, 255, 255, 0.8)',
          'text-muted': 'rgba(255, 255, 255, 0.6)',
          'text-light': 'rgba(255, 255, 255, 0.4)',
          'text-accent': '#22D3EE',
          'text-white': '#FFFFFF',

          // Modern border system
          'border-light': 'rgba(34, 211, 238, 0.1)',
          'border-medium': 'rgba(34, 211, 238, 0.2)',
          'border-strong': 'rgba(34, 211, 238, 0.3)',
          'border-accent': '#22D3EE',

          // Enhanced button colors
          'button-primary': '#0891B2',
          'button-primary-hover': '#06B6D4',
          'button-secondary': '#22D3EE',
          'button-secondary-hover': '#7DD3FC',
          'button-glass': 'rgba(34, 211, 238, 0.1)',

          // Modern status colors
          'success': '#10B981',
          'success-light': 'rgba(16, 185, 129, 0.1)',
          'warning': '#F59E0B',
          'warning-light': 'rgba(245, 158, 11, 0.1)',
          'error': '#EF4444',
          'error-light': 'rgba(239, 68, 68, 0.1)',
          'info': '#3B82F6',
          'info-light': 'rgba(59, 130, 246, 0.1)',

          // Legacy compatibility (updated for modern theme)
          'primary-blue': '#0891B2',
          'secondary-blue': '#22D3EE',
          'light-blue': '#7DD3FC',
          'powder-blue': '#A5F3FC',
          white: '#FFFFFF',
          gold: '#22D3EE',
          'light-gold': '#7DD3FC',
          bronze: '#0891B2',
        },
        // Shadcn UI Colors
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
        'fade-in': {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        'slide-in': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        'gentle-float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'subtle-glow': {
          '0%, 100%': { boxShadow: '0 0 20px rgba(102, 182, 196, 0.3)' },
          '50%': { boxShadow: '0 0 30px rgba(102, 182, 196, 0.5)' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'fade-in': 'fade-in 0.6s ease-out',
        'slide-in': 'slide-in 0.5s ease-out',
        'gentle-float': 'gentle-float 6s ease-in-out infinite',
        'subtle-glow': 'subtle-glow 4s ease-in-out infinite',
      },
      fontFamily: {
        'sans': ['Open Sans', 'Arial', 'sans-serif'],
      },
      boxShadow: {
        'netcare': '0 4px 20px rgba(102, 182, 196, 0.1)',
        'netcare-lg': '0 8px 40px rgba(102, 182, 196, 0.15)',
        'netcare-xl': '0 12px 60px rgba(102, 182, 196, 0.2)',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
export default config;