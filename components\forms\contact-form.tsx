'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';

interface ContactFormProps {
  onSubmit?: (data: any) => void;
}

export default function ContactForm({ onSubmit }: ContactFormProps) {
  return (
    <Card className="netcare-card">
      <CardHeader>
        <CardTitle className="text-netcare-white text-xl">
          Contact Form
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-netcare-white/70">Contact form component will be implemented here.</p>
        <div className="flex justify-end pt-4">
          <Button className="netcare-button" onClick={() => onSubmit?.({})}>
            Submit
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
