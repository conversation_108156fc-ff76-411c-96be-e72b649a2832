# Code Splitting Strategy for Netcare Claims Portal

## Overview

This document outlines the comprehensive code splitting strategy implemented for the Netcare Claims Portal to optimize performance, reduce bundle sizes, and improve user experience.

## Current Bundle Analysis (Before Optimization)

- **Dashboard page**: 29.9 kB (largest page)
- **Support page**: 31.9 kB (second largest)
- **Shared JS**: 101 kB (significant shared bundle)
- **Individual pages**: 2.84-6.72 kB (reasonable sizes)

## Code Splitting Strategies Implemented

### 1. Route-Based Code Splitting ✅

**Status**: Already implemented by Next.js App Router
- Each page is automatically split into separate chunks
- Pages load only when navigated to
- Provides baseline performance optimization

### 2. Component-Based Code Splitting 🆕

**Heavy Components Split**:
- `AnalyticsCharts` - Dashboard analytics (Recharts library)
- `UploadForm` - Complex file upload forms
- `ClaimForm` - Claim submission forms
- `ContactForm` - Support contact forms

**Implementation**:
```typescript
// Lazy-loaded analytics component
const LazyAnalytics = lazy(() => import('@/components/dashboard/lazy-analytics'));

// Usage with Suspense
<Suspense fallback={<ChartSkeleton />}>
  <LazyAnalytics claims={claims} />
</Suspense>
```

### 3. Third-Party Library Splitting 🆕

**Libraries Optimized**:
- **Recharts** (2.12.7) - Chart library for analytics
- **React Hook Form** (7.53.0) - Form handling
- **Date-fns** (3.6.0) - Date utilities
- **Lucide React** (0.446.0) - Icon library
- **Radix UI** components - UI primitives

**Webpack Configuration**:
```javascript
splitChunks: {
  cacheGroups: {
    charts: {
      name: 'charts',
      test: /[\\/]node_modules[\\/](recharts|d3-.*)[\\/]/,
      chunks: 'all',
      priority: 30,
    },
    ui: {
      name: 'ui',
      test: /[\\/]node_modules[\\/]@radix-ui[\\/]/,
      chunks: 'all',
      priority: 25,
    },
    // ... more cache groups
  }
}
```

### 4. Package Import Optimization 🆕

**Next.js Experimental Features**:
```javascript
experimental: {
  optimizePackageImports: [
    'lucide-react',
    '@radix-ui/react-*',
    'date-fns'
  ]
}
```

## Implementation Files

### Core Components

1. **`components/dashboard/analytics-charts.tsx`**
   - Heavy analytics component with Recharts
   - Separated from main dashboard bundle

2. **`components/dashboard/lazy-analytics.tsx`**
   - Lazy wrapper with loading skeleton
   - Provides smooth loading experience

3. **`components/forms/lazy-form-wrapper.tsx`**
   - Universal lazy form loader
   - Supports multiple form types

4. **`lib/dynamic-imports.ts`**
   - Utility functions for dynamic imports
   - Preloading capabilities
   - Consistent loading states

### Configuration Files

1. **`next.config.js`**
   - Webpack bundle splitting configuration
   - Package import optimization
   - Bundle analyzer integration

2. **`components/performance/performance-monitor.tsx`**
   - Performance monitoring utilities
   - Development metrics logging

## Expected Performance Improvements

### Bundle Size Reduction
- **Dashboard page**: ~29.9 kB → ~15-20 kB (30-35% reduction)
- **Charts chunk**: Separate ~8-12 kB chunk (loaded on demand)
- **Forms chunk**: Separate ~5-8 kB chunk (loaded on demand)
- **UI components**: Better caching across routes

### Loading Performance
- **Initial page load**: Faster due to smaller initial bundles
- **Navigation**: Improved with preloading strategies
- **User experience**: Loading skeletons prevent layout shifts

### Caching Benefits
- **Library chunks**: Better browser caching
- **Component chunks**: Cached across route changes
- **Vendor splitting**: Reduced cache invalidation

## Usage Examples

### Lazy Loading Components

```typescript
// Dashboard with lazy analytics
import LazyAnalytics from '@/components/dashboard/lazy-analytics';

export default function Dashboard() {
  return (
    <div>
      <StatsCards />
      <LazyAnalytics claims={claims} />
      <ClaimsGrid />
    </div>
  );
}
```

### Dynamic Form Loading

```typescript
// Dynamic form based on type
import LazyFormWrapper from '@/components/forms/lazy-form-wrapper';

export default function FormPage() {
  return (
    <LazyFormWrapper 
      formType="upload"
      title="Upload Documents"
      onSubmit={handleSubmit}
    />
  );
}
```

### Preloading on Hover

```typescript
import { preloadRouteComponent } from '@/lib/dynamic-imports';

export default function NavigationLink({ href, children }) {
  return (
    <Link 
      href={href}
      {...preloadRouteComponent(href)}
    >
      {children}
    </Link>
  );
}
```

## Monitoring and Analysis

### Bundle Analysis
```bash
# Analyze bundle composition
ANALYZE=true npm run build
```

### Performance Monitoring
```typescript
// Add to components for monitoring
import PerformanceMonitor from '@/components/performance/performance-monitor';

export default function MyComponent() {
  return (
    <PerformanceMonitor componentName="MyComponent">
      {/* Component content */}
    </PerformanceMonitor>
  );
}
```

### Development Debugging
```typescript
// Add to root layout for dev metrics
import { PerformanceDebugger } from '@/components/performance/performance-monitor';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <PerformanceDebugger />
      </body>
    </html>
  );
}
```

## Best Practices

### 1. Component Splitting Guidelines
- Split components > 10kB
- Split third-party library integrations
- Split rarely used features
- Keep critical path components in main bundle

### 2. Loading States
- Always provide loading skeletons
- Match skeleton structure to actual content
- Use consistent loading patterns

### 3. Preloading Strategy
- Preload on hover/focus for navigation
- Preload critical components after initial load
- Use intersection observer for viewport-based loading

### 4. Monitoring
- Track bundle sizes in CI/CD
- Monitor loading performance metrics
- Set performance budgets

## Future Optimizations

### 1. Advanced Splitting
- User role-based splitting
- Feature flag-based splitting
- A/B test variant splitting

### 2. Service Worker Integration
- Precache critical chunks
- Background chunk updates
- Offline fallbacks

### 3. Edge Computing
- Edge-side component rendering
- Regional bundle optimization
- CDN-based chunk delivery

## Maintenance

### Regular Tasks
- Monitor bundle size changes
- Update splitting strategies based on usage
- Review and optimize chunk boundaries
- Update performance budgets

### Metrics to Track
- First Contentful Paint (FCP)
- Largest Contentful Paint (LCP)
- Time to Interactive (TTI)
- Bundle size per route
- Cache hit rates
