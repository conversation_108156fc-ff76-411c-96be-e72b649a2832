'use client';

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { 
  <PERSON><PERSON>hart as PieChartIcon,
  BarChart as BarChartIcon,
  LineChart as LineChartIcon,
  AreaChart as AreaChartIcon
} from 'lucide-react';
import {
  PieChart,
  Pie,
  LineChart,
  Line,
  BarChart,
  Bar,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';

interface Claim {
  id: string;
  type: 'Medical Aid Statement' | 'Provider Document';
  status: 'Submitted' | 'Processing' | 'Processed' | 'Completed' | 'Rejected' | 'Requires Attention';
  submittedDate: string;
  policyNumber?: string;
  provider?: string;
  serviceDate?: string;
  amountPaid: string;
  amountUnpaid: string;
  shortfall?: string;
}

interface AnalyticsChartsProps {
  claims: Claim[];
}

export default function AnalyticsCharts({ claims }: AnalyticsChartsProps) {
  // Prepare data for charts
  const statusCounts = claims.reduce((acc, claim) => {
    acc[claim.status] = (acc[claim.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const statusDistributionData = Object.entries(statusCounts).map(([name, value]) => ({ name, value }));

  // Status colors for pie chart
  const statusColors = {
    'Submitted': '#38bdf8', // netcare-cyan
    'Processing': '#7dd3fc', // netcare-light-cyan
    'Processed': '#0ea5e9', // netcare-cyan variant
    'Completed': '#0284c7', // netcare-light-cyan variant
    'Rejected': '#94a3b8', // netcare-silver
    'Requires Attention': '#bae6fd', // netcare-pale-cyan
  };

  // Timeline Chart - Mock data (would be replaced with real data)
  const timelineData = [
    { month: 'Jan', claims: 4 },
    { month: 'Feb', claims: 6 },
    { month: 'Mar', claims: 8 },
    { month: 'Apr', claims: 5 },
    { month: 'May', claims: 12 },
    { month: 'Jun', claims: 6 },
  ];

  // Payment Analysis Chart
  const paymentData = claims.map(claim => {
    const paid = parseFloat(claim.amountPaid.replace('R', '').replace(',', ''));
    const unpaid = parseFloat(claim.amountUnpaid.replace('R', '').replace(',', ''));
    return {
      name: claim.id,
      paid: isNaN(paid) ? 0 : paid,
      unpaid: isNaN(unpaid) ? 0 : unpaid
    };
  });

  // Monthly Claims Activity Chart - Mock data
  const monthlyActivityData = [
    { month: 'Jan', submitted: 3, completed: 2 },
    { month: 'Feb', submitted: 5, completed: 3 },
    { month: 'Mar', submitted: 7, completed: 4 },
    { month: 'Apr', submitted: 4, completed: 6 },
    { month: 'May', submitted: 10, completed: 7 },
    { month: 'Jun', submitted: 6, completed: 5 },
  ];

  return (
    <div className="mb-6">
      <h3 className="text-2xl font-bold text-gray-800 mb-4">Claims Analytics</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Claims Status Distribution Chart */}
        <Card className="netcare-card group transition-all duration-500">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium text-netcare-white flex items-center gap-2">
                <PieChartIcon className="w-4 h-4 text-netcare-cyan" />
                Claims Status Distribution
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 h-[200px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={statusDistributionData}
                  cx="50%"
                  cy="50%"
                  outerRadius={60}
                  fill="#38bdf8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {statusDistributionData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={statusColors[entry.name as keyof typeof statusColors] || '#94a3b8'} 
                    />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Claims Timeline Chart */}
        <Card className="netcare-card group transition-all duration-500">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium text-netcare-white flex items-center gap-2">
                <LineChartIcon className="w-4 h-4 text-netcare-cyan" />
                Claims Timeline
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 h-[200px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={timelineData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="month" stroke="#9ca3af" />
                <YAxis stroke="#9ca3af" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#1f2937', 
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#f9fafb'
                  }} 
                />
                <Line 
                  type="monotone" 
                  dataKey="claims" 
                  stroke="#38bdf8" 
                  strokeWidth={2}
                  dot={{ fill: '#38bdf8', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Payment Analysis Chart */}
        <Card className="netcare-card group transition-all duration-500">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium text-netcare-white flex items-center gap-2">
                <BarChartIcon className="w-4 h-4 text-netcare-cyan" />
                Payment Analysis
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 h-[200px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={paymentData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="name" stroke="#9ca3af" />
                <YAxis stroke="#9ca3af" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#1f2937', 
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#f9fafb'
                  }} 
                />
                <Bar dataKey="paid" fill="#38bdf8" />
                <Bar dataKey="unpaid" fill="#f59e0b" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Monthly Claims Activity Chart */}
        <Card className="netcare-card group transition-all duration-500">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium text-netcare-white flex items-center gap-2">
                <AreaChartIcon className="w-4 h-4 text-netcare-cyan" />
                Monthly Claims Activity
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 h-[200px]">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={monthlyActivityData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="month" stroke="#9ca3af" />
                <YAxis stroke="#9ca3af" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#1f2937', 
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#f9fafb'
                  }} 
                />
                <Area 
                  type="monotone" 
                  dataKey="submitted" 
                  stackId="1"
                  stroke="#38bdf8" 
                  fill="#38bdf8" 
                  fillOpacity={0.6}
                />
                <Area 
                  type="monotone" 
                  dataKey="completed" 
                  stackId="1"
                  stroke="#10b981" 
                  fill="#10b981" 
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
