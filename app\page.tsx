import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  CheckCircle, 
  PlusCircle, 
  FileText, 
  Clock, 
  DollarSign,
  Search,
  Filter
} from 'lucide-react';

export default function Home() {
  // Summary card data
  const summaryCards = [
    {
      title: 'Total Claims',
      value: '6',
      icon: <FileText className="w-5 h-5 text-white" />,
      color: 'bg-netcare-slate'
    },
    {
      title: 'Completed',
      value: '1',
      icon: <CheckCircle className="w-5 h-5 text-white" />,
      color: 'bg-netcare-slate'
    },
    {
      title: 'Pending',
      value: '2',
      icon: <Clock className="w-5 h-5 text-white" />,
      color: 'bg-netcare-slate'
    },
    {
      title: 'Total Paid',
      value: 'R2,250',
      icon: <DollarSign className="w-5 h-5 text-white" />,
      color: 'bg-netcare-slate'
    }
  ];
  
  // Sample claims data
  const claims = [
    {
      id: 'claim-1',
      type: 'Medical Aid Statement',
      status: 'Submitted',
      date: 'May 29, 2025',
      policyNumber: 'POL-100000',
      amountPaid: 'R750.00',
      amountUnpaid: 'R250.00'
    },
    {
      id: 'claim-2',
      type: 'Provider Document',
      status: 'Processing',
      date: 'May 25, 2025',
      policyNumber: 'POL-100001',
      provider: 'Dr. Smith Medical Practice',
      serviceDate: 'May 18, 2025',
      shortfall: 'R500.00',
      amountPaid: 'R0.00',
      amountUnpaid: 'R500.00'
    },
    {
      id: 'claim-3',
      type: 'Medical Aid Statement',
      status: 'Requires Attention',
      date: 'May 22, 2025',
      policyNumber: 'POL-100002',
      amountPaid: 'R750.00',
      amountUnpaid: 'R250.00'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Claims Page Header Section */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h2 className="text-3xl font-bold text-netcare-text-primary tracking-tight sm:text-4xl">Your Claims</h2>
            <p className="mt-2 text-lg text-netcare-text-secondary">
              Manage and track your medical claims.
            </p>
          </div>
          <Button size="lg" className="bg-netcare-button-primary hover:bg-netcare-button-primary-hover text-netcare-text-white mt-4 md:mt-0">
            <PlusCircle size={20} className="mr-2" />
            Start a New Claim
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {summaryCards.map((card, index) => (
            <div key={index} className={`${card.color} rounded-lg p-6 text-white shadow-md flex justify-between items-center`}>
              <div>
                <h3 className="text-sm font-medium text-netcare-text-light mb-1">{card.title}</h3>
                <p className="text-3xl font-bold">{card.value}</p>
              </div>
              <div className="rounded-full p-2 bg-white/10">
                {card.icon}
              </div>
            </div>
          ))}
        </div>

        {/* Search and Filter */}
        <div className="relative mb-8">
          <div className="flex items-center">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-netcare-text-secondary h-4 w-4" />
              <Input 
                className="pl-10 bg-white border-netcare-border-light rounded-lg focus:border-netcare-border-accent focus:ring-1 focus:ring-netcare-border-accent" 
                placeholder="Search claims by ID, type, provider, or policy number..." 
              />
            </div>
            <div className="ml-2 flex items-center">
              <Button variant="outline" className="border-netcare-border-light text-netcare-text-secondary flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <span>All Claims</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Claims Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {claims.map((claim, index) => (
            <Card key={index} className="bg-netcare-slate text-white shadow-md overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    <div>
                      <p className="font-medium">{claim.id}</p>
                      <p className="text-sm text-netcare-text-light">{claim.type}</p>
                    </div>
                  </div>
                  <Badge className={`${claim.status === 'Submitted' ? 'bg-netcare-button-primary' : claim.status === 'Processing' ? 'bg-amber-500' : 'bg-red-500'} text-white`}>
                    {claim.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="border-t border-white/10 pt-4 mt-2">
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-netcare-text-light">Submitted</span>
                    <span>{claim.date}</span>
                  </div>
                  <div className="flex justify-between text-sm mb-4">
                    <span className="text-netcare-text-light">Policy Number</span>
                    <span>{claim.policyNumber}</span>
                  </div>
                  
                  {claim.provider && (
                    <div className="mb-2">
                      <p className="text-netcare-text-light text-sm">Provider:</p>
                      <p className="text-sm">{claim.provider}</p>
                    </div>
                  )}
                  
                  {claim.serviceDate && (
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-netcare-text-light">Service Date:</span>
                      <span>{claim.serviceDate}</span>
                    </div>
                  )}
                  
                  {claim.shortfall && (
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-netcare-text-light">Shortfall</span>
                      <span>{claim.shortfall}</span>
                    </div>
                  )}
                  
                  <div className="flex justify-between border-t border-white/10 pt-4 mt-2">
                    <div>
                      <p className="text-netcare-text-light text-xs">Amount Paid</p>
                      <p className="font-medium">{claim.amountPaid}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-netcare-text-light text-xs">Amount Unpaid</p>
                      <p className="font-medium">{claim.amountUnpaid}</p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 text-right">
                  <Button variant="ghost" className="text-netcare-cyan hover:text-netcare-cyan/80 p-0 h-auto">
                    View Details
                    <span className="ml-1">→</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-8">
          <div className="text-sm text-netcare-text-secondary">
            Showing <span className="font-medium">1</span> to <span className="font-medium">3</span> of <span className="font-medium">6</span> claims
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="border-netcare-border-light text-netcare-text-secondary" disabled>
              Previous
            </Button>
            <Button variant="outline" size="sm" className="border-netcare-border-light bg-netcare-button-primary text-white">
              1
            </Button>
            <Button variant="outline" size="sm" className="border-netcare-border-light text-netcare-text-secondary">
              2
            </Button>
            <Button variant="outline" size="sm" className="border-netcare-border-light text-netcare-text-secondary">
              Next
            </Button>
          </div>
        </div>
        {/* Status Section - No longer needed with the new claims dashboard */}
      </div>
    </div>
  );
}