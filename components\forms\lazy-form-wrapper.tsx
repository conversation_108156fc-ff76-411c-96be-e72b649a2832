'use client';

import React, { Suspense, lazy } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

// Lazy load form components
const UploadForm = lazy(() => import('./upload-form'));
const ClaimForm = lazy(() => import('./claim-form'));
const ContactForm = lazy(() => import('./contact-form'));

interface LazyFormWrapperProps {
  formType: 'upload' | 'claim' | 'contact';
  title?: string;
  description?: string;
  onSubmit?: (data: any) => void;
  [key: string]: any;
}

// Loading skeleton for forms
const FormSkeleton = ({ title, description }: { title?: string; description?: string }) => (
  <Card className="netcare-card">
    <CardHeader>
      {title && (
        <CardTitle className="text-netcare-white text-xl">
          {title}
        </CardTitle>
      )}
      {description && (
        <p className="text-netcare-white/70">{description}</p>
      )}
    </CardHeader>
    <CardContent className="space-y-6">
      {/* Form field skeletons */}
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="space-y-2">
          <div className="h-4 bg-netcare-white/20 rounded animate-pulse w-24"></div>
          <div className="h-10 bg-netcare-white/10 rounded animate-pulse"></div>
        </div>
      ))}
      
      {/* Button skeleton */}
      <div className="flex justify-end pt-4">
        <Button disabled className="netcare-button">
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          Loading...
        </Button>
      </div>
    </CardContent>
  </Card>
);

export default function LazyFormWrapper({ 
  formType, 
  title, 
  description, 
  onSubmit,
  ...props 
}: LazyFormWrapperProps) {
  const renderForm = () => {
    switch (formType) {
      case 'upload':
        return <UploadForm onSubmit={onSubmit} {...props} />;
      case 'claim':
        return <ClaimForm onSubmit={onSubmit} {...props} />;
      case 'contact':
        return <ContactForm onSubmit={onSubmit} {...props} />;
      default:
        return <div>Unknown form type</div>;
    }
  };

  return (
    <Suspense fallback={<FormSkeleton title={title} description={description} />}>
      {renderForm()}
    </Suspense>
  );
}
