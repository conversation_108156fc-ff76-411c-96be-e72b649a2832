'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  ArrowLeft,
  FileText, 
  User, 
  LogOut, 
  HelpCircle,
  Plus,
  File,
  Edit3,
  CheckCircle,
  Eye,
  Download,
  Save,
  AlertCircle,
  ChevronRight,
  Clock,
  Check
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { cn } from '@/lib/utils';

interface DocumentInfo {
  policyNumber: string;
  patientName: string;
  medicalAidScheme: string;
  amountPaid: string;
  amountUnpaid: string;
  reasonsForNonPayment: string;
  claimDate: string;
  claimReference: string;
  planType: string;
  coveragePeriod: string;
  dependentCode: string;
  serviceProvider: string;
}

interface UploadedDocument {
  id: string;
  name: string;
  size: string;
  type: string;
  dateUploaded: string;
  status: 'processed' | 'analyzing' | 'error';
}

export default function ReviewClaimPage() {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeDocument, setActiveDocument] = useState('1');
  const [editedFields, setEditedFields] = useState<string[]>([]);
  
  const [documentInfo, setDocumentInfo] = useState<DocumentInfo>({
    policyNumber: 'POL-12345',
    patientName: 'John Doe',
    medicalAidScheme: 'HealthCare Plus',
    amountPaid: '750',
    amountUnpaid: '250',
    reasonsForNonPayment: 'Exceeds annual limit',
    claimDate: '27 May 2025',
    claimReference: 'CLM-78901',
    planType: 'Comprehensive Plus',
    coveragePeriod: '01 May 2025 - 27 May 2025',
    dependentCode: '00 (Main Member)',
    serviceProvider: 'City Medical Center'
  });

  const [uploadedDocuments, setUploadedDocuments] = useState<UploadedDocument[]>([
    {
      id: '1',
      name: 'Medical_Aid_Statement.pdf',
      size: '2.2 MB',
      type: 'Medical Aid Statement',
      dateUploaded: '27 May 2025',
      status: 'processed'
    },
    {
      id: '2',
      name: 'Provider_Invoice.pdf',
      size: '1.8 MB',
      type: 'Medical Provider Statement',
      dateUploaded: '27 May 2025',
      status: 'processed'
    }
  ]);

  // Track progress through the claim process
  const steps = [
    { name: 'Claim Details', status: 'complete' },
    { name: 'Upload Documents', status: 'complete' },
    { name: 'Review', status: 'current' },
    { name: 'Submit', status: 'upcoming' }
  ];

  const handleInputChange = (field: keyof DocumentInfo, value: string) => {
    if (!editedFields.includes(field)) {
      setEditedFields([...editedFields, field]);
    }
    
    setDocumentInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleToggleEdit = () => {
    if (isEditing) {
      // Save changes
      setIsSaving(true);
      // Simulate saving delay
      setTimeout(() => {
        setIsSaving(false);
        setIsEditing(false);
        setEditedFields([]);
        // Here you would normally save the changes to your backend
      }, 1500);
    } else {
      // Enter edit mode
      setIsEditing(true);
    }
  };

  const handleSubmitClaim = () => {
    if (editedFields.length > 0 && isEditing) {
      return; // Don't allow submission while editing
    }
    
    setIsSubmitting(true);
    console.log('Submitting claim with data:', documentInfo);
    
    // Simulate API call with delay
    setTimeout(() => {
      setIsSubmitting(false);
      router.push('/claim-submitted');
    }, 2000);
  };

  const handleCancel = () => {
    if (isEditing) {
      // If in edit mode, cancel editing and reset form
      setIsEditing(false);
      setEditedFields([]);
      // Reset form to original values
      setDocumentInfo({
        policyNumber: "POL-12345678",
        patientName: "John Smith",
        medicalAidScheme: "Netcare Medical Aid",
        amountPaid: "1,250.00",
        amountUnpaid: "750.00",
        reasonsForNonPayment: "Benefit limit reached for dental procedures.",
        claimDate: "2023-06-15",
        claimReference: "REF-98765432",
        planType: "Premium Health",
        coveragePeriod: "Jan 2023 - Dec 2023",
        dependentCode: "DEP-001",
        serviceProvider: "Dr. Jane Williams"
      });
    } else {
      // If not in edit mode, go back to documents page
      router.push('/upload-documents');
    }
  };
  
  const handleViewDocument = (id: string) => {
    setActiveDocument(id);
  };

  return (
    // Note: MainLayout is applied in the root layout.tsx
    <div className="max-w-6xl mx-auto px-6 py-10">
        {/* Back Navigation */}
        <div className="mb-6">
          <Link 
            href="/upload-documents" 
            className="inline-flex items-center text-netcare-white/70 hover:text-netcare-gold transition-colors group"
          >
            <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
            Back to Upload Documents
          </Link>
        </div>

        {/* Progress Steps */}
        <div className="mb-10">
          <div className="flex items-center justify-between max-w-3xl">
            {steps.map((step, index) => (
              <div key={step.name} className="flex items-center">
                <div className={cn(
                  "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all",
                  step.status === 'complete' ? "bg-netcare-cyan border-netcare-cyan text-white" : 
                  step.status === 'current' ? "bg-netcare-dark-blue border-netcare-gold text-netcare-gold" :
                  "bg-transparent border-netcare-white/30 text-netcare-white/50"
                )}>
                  {step.status === 'complete' ? (
                    <Check className="w-5 h-5" />
                  ) : step.status === 'current' ? (
                    <span className="text-sm font-bold">{index + 1}</span>
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </div>
                <div className="ml-3">
                  <p className={cn(
                    "text-sm font-medium",
                    step.status === 'complete' ? "text-netcare-cyan" :
                    step.status === 'current' ? "text-netcare-gold" :
                    "text-netcare-white/50"
                  )}>
                    {step.name}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={cn(
                    "w-12 h-0.5 mx-2",
                    step.status === 'complete' ? "bg-netcare-cyan" : "bg-netcare-white/20"
                  )} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Page Header */}
        <div className="mb-10">
          <h2 className="text-4xl font-bold text-netcare-gold mb-4">Review Claim Details</h2>
          <p className="text-netcare-white/90 text-lg leading-relaxed">
            We've extracted information from your uploaded documents. Please review and make any necessary corrections before submitting your claim.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Left Column - Uploaded Documents */}
          <div className="space-y-8">
            <Card className="netcare-card border-netcare-gold/30 overflow-hidden">
              <div className="h-1.5 bg-gradient-to-r from-netcare-cyan to-netcare-light-cyan" />
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-netcare-gold text-xl font-bold">Uploaded Documents</CardTitle>
                <span className="text-netcare-white/70 text-sm">{uploadedDocuments.length} files</span>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                {uploadedDocuments.map((doc) => (
                  <div
                    key={doc.id}
                    onClick={() => handleViewDocument(doc.id)}
                    className={cn(
                      "flex items-center justify-between p-4 rounded-xl border transition-all duration-300",
                      activeDocument === doc.id 
                        ? "bg-netcare-cyan/20 border-netcare-cyan shadow-lg" 
                        : "bg-white/5 border-netcare-gold/20 hover:bg-white/10",
                      "cursor-pointer"
                    )}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={cn(
                        "p-3 rounded-lg flex items-center justify-center",
                        activeDocument === doc.id ? "bg-netcare-cyan/30" : "bg-cyan-400/10"
                      )}>
                        <File className={cn(
                          "w-5 h-5", 
                          activeDocument === doc.id ? "text-netcare-cyan" : "text-cyan-400"
                        )} />
                      </div>
                      <div>
                        <p className="font-medium text-netcare-white">{doc.name}</p>
                        <div className="flex items-center mt-1">
                          <span className="text-xs text-netcare-white/60">{doc.size}</span>
                          <span className="mx-2 text-netcare-white/30">•</span>
                          <span className="text-xs text-netcare-white/60">{doc.type}</span>
                          {doc.status === 'processed' && (
                            <span className="ml-2 flex items-center text-xs text-emerald-400">
                              <Check className="w-3 h-3 mr-1" /> Processed
                            </span>
                          )}
                          {doc.status === 'analyzing' && (
                            <span className="ml-2 flex items-center text-xs text-amber-400">
                              <Clock className="w-3 h-3 mr-1 animate-spin" /> Analyzing
                            </span>
                          )}
                          {doc.status === 'error' && (
                            <span className="ml-2 flex items-center text-xs text-red-400">
                              <AlertCircle className="w-3 h-3 mr-1" /> Error
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewDocument(doc.id);
                        }}
                        className={cn(
                          "rounded-full h-8 w-8 p-0",
                          activeDocument === doc.id
                            ? "text-netcare-cyan bg-netcare-cyan/20 hover:bg-netcare-cyan/30"
                            : "text-netcare-white/60 hover:text-cyan-400 hover:bg-cyan-400/10"
                        )}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => e.stopPropagation()}
                        className="text-netcare-white/60 hover:text-cyan-400 hover:bg-cyan-400/10 rounded-full h-8 w-8 p-0"
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Document Preview */}
            <Card className="netcare-card border-netcare-gold/30 overflow-hidden">
              <div className="h-1.5 bg-gradient-to-r from-netcare-gold/70 to-netcare-gold/30" />
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <div>
                  <CardTitle className="text-netcare-gold text-xl font-bold">Document Preview</CardTitle>
                  <CardDescription className="text-netcare-white/60">
                    {uploadedDocuments.find(doc => doc.id === activeDocument)?.name || "Select a document to view details"}
                  </CardDescription>
                </div>
                {activeDocument && (
                  <Button 
                    size="sm" 
                    className="netcare-button-secondary"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download PDF
                  </Button>
                )}
              </CardHeader>
              <CardContent className="relative min-h-[450px] p-0 overflow-hidden">
                {activeDocument ? (
                  <div className="w-full h-full relative">
                    {/* Watermark */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-5 pointer-events-none">
                      <FileText className="w-64 h-64 text-netcare-gold" />
                    </div>
                    
                    <div className="p-6 relative z-10">
                      {/* Document Header */}
                      <div className="flex justify-between items-start mb-8">
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            {/* Status indicator */}
                            <div className={cn(
                              "w-2 h-2 rounded-full",
                              uploadedDocuments.find(doc => doc.id === activeDocument)?.status === 'processed' ? "bg-emerald-400" : 
                              uploadedDocuments.find(doc => doc.id === activeDocument)?.status === 'analyzing' ? "bg-amber-400" : "bg-red-400"
                            )} />
                            <h3 className="text-netcare-gold font-semibold text-lg">
                              {uploadedDocuments.find(doc => doc.id === activeDocument)?.name || 'Medical Statement'}
                            </h3>
                          </div>
                          <p className="text-netcare-white/70 text-sm">
                            {uploadedDocuments.find(doc => doc.id === activeDocument)?.dateUploaded || ''} · 
                            {uploadedDocuments.find(doc => doc.id === activeDocument)?.size || ''} · 
                            <span className={cn(
                              uploadedDocuments.find(doc => doc.id === activeDocument)?.status === 'processed' ? "text-emerald-400" : 
                              uploadedDocuments.find(doc => doc.id === activeDocument)?.status === 'analyzing' ? "text-amber-400" : "text-red-400"
                            )}>
                              {uploadedDocuments.find(doc => doc.id === activeDocument)?.status === 'processed' ? "Processed" : 
                               uploadedDocuments.find(doc => doc.id === activeDocument)?.status === 'analyzing' ? "Analyzing" : "Error"}
                            </span>
                          </p>
                        </div>
                        <div className="bg-netcare-dark-blue/80 p-3 rounded-md border border-netcare-gold/20">
                          <FileText className="w-6 h-6 text-netcare-gold" />
                        </div>
                      </div>

                      {/* Document Content */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="space-y-6">
                          {/* Provider Information */}
                          <div className="bg-netcare-dark-blue/40 p-4 rounded-lg border-l-4 border-netcare-cyan">
                            <h4 className="text-netcare-cyan text-sm font-medium mb-2">Provider Information</h4>
                            <p className="text-netcare-white text-base font-medium">{documentInfo.serviceProvider}</p>
                            <div className="mt-2 grid grid-cols-2 gap-2">
                              <div>
                                <p className="text-netcare-white/60 text-xs">Date of Service</p>
                                <p className="text-netcare-white text-sm">{documentInfo.claimDate}</p>
                              </div>
                              <div>
                                <p className="text-netcare-white/60 text-xs">Reference</p>
                                <p className="text-netcare-white text-sm">{documentInfo.claimReference}</p>
                              </div>
                            </div>
                          </div>

                          {/* Member Information */}
                          <div className="bg-netcare-dark-blue/40 p-4 rounded-lg border-l-4 border-netcare-gold">
                            <h4 className="text-netcare-gold text-sm font-medium mb-2">Member Information</h4>
                            <p className="text-netcare-white text-base font-medium">{documentInfo.patientName}</p>
                            <div className="mt-2 grid grid-cols-2 gap-2">
                              <div>
                                <p className="text-netcare-white/60 text-xs">Policy Number</p>
                                <p className="text-netcare-white text-sm">{documentInfo.policyNumber}</p>
                              </div>
                              <div>
                                <p className="text-netcare-white/60 text-xs">Dependent Code</p>
                                <p className="text-netcare-white text-sm">{documentInfo.dependentCode}</p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-6">
                          {/* Payment Information */}
                          <div className="bg-netcare-dark-blue/40 p-4 rounded-lg border-l-4 border-emerald-400">
                            <h4 className="text-emerald-400 text-sm font-medium mb-2">Payment Information</h4>
                            <div className="grid grid-cols-2 gap-4 mb-4">
                              <div>
                                <p className="text-netcare-white/60 text-xs">Amount Paid</p>
                                <p className="text-emerald-400 font-bold text-lg">R {documentInfo.amountPaid}</p>
                              </div>
                              <div>
                                <p className="text-netcare-white/60 text-xs">Amount Unpaid</p>
                                <p className="text-amber-400 font-bold text-lg">R {documentInfo.amountUnpaid}</p>
                              </div>
                            </div>
                            <div>
                              <p className="text-netcare-white/60 text-xs">Reasons for Non-Payment</p>
                              <p className="text-netcare-white text-sm mt-1">{documentInfo.reasonsForNonPayment}</p>
                            </div>
                          </div>

                          {/* Plan Information */}
                          <div className="bg-netcare-dark-blue/40 p-4 rounded-lg border-l-4 border-netcare-light-cyan">
                            <h4 className="text-netcare-light-cyan text-sm font-medium mb-2">Plan Information</h4>
                            <p className="text-netcare-white text-base font-medium">{documentInfo.planType}</p>
                            <div className="mt-2">
                              <p className="text-netcare-white/60 text-xs">Coverage Period</p>
                              <p className="text-netcare-white text-sm">{documentInfo.coveragePeriod}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-16">
                    <div className="bg-netcare-dark-blue/20 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FileText className="w-12 h-12 text-netcare-dark-blue/30" />
                    </div>
                    <p className="text-netcare-white/50 mb-2">No document selected</p>
                    <p className="text-netcare-white/30 text-sm max-w-xs mx-auto">Select a document from the list on the left to view its details</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Document Information */}
          <div className="space-y-8">
            <Card className="netcare-card border-netcare-gold/30 overflow-hidden">
              <div className="h-1.5 bg-gradient-to-r from-netcare-cyan to-netcare-light-cyan" />
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-netcare-gold text-xl font-bold">Document Information</CardTitle>
                <Button
                  variant={isEditing ? "outline" : "ghost"}
                  size="sm"
                  onClick={handleToggleEdit}
                  disabled={isSaving}
                  className={cn(
                    "transition-all duration-300",
                    isEditing 
                      ? "text-netcare-gold border-netcare-gold hover:bg-netcare-gold/10 hover:text-netcare-gold" 
                      : "text-cyan-400 hover:text-cyan-300 hover:bg-cyan-400/10"
                  )}
                >
                  {isSaving ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : isEditing ? (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Changes
                    </>
                  ) : (
                    <>
                      <Edit3 className="w-4 h-4 mr-2" />
                      Edit Information
                    </>
                  )}
                </Button>
              </CardHeader>
              <CardContent className="space-y-6 pt-4">
                <div className="grid gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="policyNumber" className={cn(
                      "font-medium",
                      editedFields.includes('policyNumber') && isEditing ? "text-netcare-gold" : "text-netcare-white/80"
                    )}>
                      Policy Number
                      {editedFields.includes('policyNumber') && isEditing && (
                        <span className="ml-2 text-xs text-netcare-gold">(Edited)</span>
                      )}
                    </Label>
                    {isEditing ? (
                      <div className="relative">
                        <Input
                          id="policyNumber"
                          value={documentInfo.policyNumber}
                          onChange={(e) => handleInputChange('policyNumber', e.target.value)}
                          className="netcare-input pr-10"
                        />
                        {editedFields.includes('policyNumber') && (
                          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-netcare-gold">
                            <Edit3 className="w-4 h-4" />
                          </div>
                        )}
                      </div>
                    ) : (
                      <p className="text-netcare-white font-semibold text-lg">{documentInfo.policyNumber}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="patientName" className={cn(
                      "font-medium",
                      editedFields.includes('patientName') && isEditing ? "text-netcare-gold" : "text-netcare-white/80"
                    )}>
                      Patient Name
                      {editedFields.includes('patientName') && isEditing && (
                        <span className="ml-2 text-xs text-netcare-gold">(Edited)</span>
                      )}
                    </Label>
                    {isEditing ? (
                      <div className="relative">
                        <Input
                          id="patientName"
                          value={documentInfo.patientName}
                          onChange={(e) => handleInputChange('patientName', e.target.value)}
                          className="netcare-input pr-10"
                        />
                        {editedFields.includes('patientName') && (
                          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-netcare-gold">
                            <Edit3 className="w-4 h-4" />
                          </div>
                        )}
                      </div>
                    ) : (
                      <p className="text-netcare-white font-semibold text-lg">{documentInfo.patientName}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="medicalAidScheme" className={cn(
                      "font-medium",
                      editedFields.includes('medicalAidScheme') && isEditing ? "text-netcare-gold" : "text-netcare-white/80"
                    )}>
                      Medical Aid Scheme
                      {editedFields.includes('medicalAidScheme') && isEditing && (
                        <span className="ml-2 text-xs text-netcare-gold">(Edited)</span>
                      )}
                    </Label>
                    {isEditing ? (
                      <div className="relative">
                        <Input
                          id="medicalAidScheme"
                          value={documentInfo.medicalAidScheme}
                          onChange={(e) => handleInputChange('medicalAidScheme', e.target.value)}
                          className="netcare-input pr-10"
                        />
                        {editedFields.includes('medicalAidScheme') && (
                          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-netcare-gold">
                            <Edit3 className="w-4 h-4" />
                          </div>
                        )}
                      </div>
                    ) : (
                      <p className="text-netcare-white font-semibold text-lg">{documentInfo.medicalAidScheme}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="amountPaid" className={cn(
                        "font-medium",
                        editedFields.includes('amountPaid') && isEditing ? "text-netcare-gold" : "text-netcare-white/80"
                      )}>
                        Amount Paid (R)
                        {editedFields.includes('amountPaid') && isEditing && (
                          <span className="ml-2 text-xs text-netcare-gold">(Edited)</span>
                        )}
                      </Label>
                      {isEditing ? (
                        <div className="relative">
                          <Input
                            id="amountPaid"
                            value={documentInfo.amountPaid}
                            onChange={(e) => handleInputChange('amountPaid', e.target.value)}
                            className="netcare-input pr-10"
                          />
                          {editedFields.includes('amountPaid') && (
                            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-netcare-gold">
                              <Edit3 className="w-4 h-4" />
                            </div>
                          )}
                        </div>
                      ) : (
                        <p className="text-emerald-400 font-bold text-xl">R {documentInfo.amountPaid}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="amountUnpaid" className={cn(
                        "font-medium",
                        editedFields.includes('amountUnpaid') && isEditing ? "text-netcare-gold" : "text-netcare-white/80"
                      )}>
                        Amount Unpaid (R)
                        {editedFields.includes('amountUnpaid') && isEditing && (
                          <span className="ml-2 text-xs text-netcare-gold">(Edited)</span>
                        )}
                      </Label>
                      {isEditing ? (
                        <div className="relative">
                          <Input
                            id="amountUnpaid"
                            value={documentInfo.amountUnpaid}
                            onChange={(e) => handleInputChange('amountUnpaid', e.target.value)}
                            className="netcare-input pr-10"
                          />
                          {editedFields.includes('amountUnpaid') && (
                            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-netcare-gold">
                              <Edit3 className="w-4 h-4" />
                            </div>
                          )}
                        </div>
                      ) : (
                        <p className="text-amber-400 font-bold text-xl">R {documentInfo.amountUnpaid}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="reasonsForNonPayment" className={cn(
                      "font-medium",
                      editedFields.includes('reasonsForNonPayment') && isEditing ? "text-netcare-gold" : "text-netcare-white/80"
                    )}>
                      Reasons for Non-Payment
                      {editedFields.includes('reasonsForNonPayment') && isEditing && (
                        <span className="ml-2 text-xs text-netcare-gold">(Edited)</span>
                      )}
                    </Label>
                    {isEditing ? (
                      <div className="relative">
                        <Textarea
                          id="reasonsForNonPayment"
                          value={documentInfo.reasonsForNonPayment}
                          onChange={(e) => handleInputChange('reasonsForNonPayment', e.target.value)}
                          className="netcare-input min-h-[100px] pr-10"
                          placeholder="Enter reasons for non-payment..."
                        />
                        {editedFields.includes('reasonsForNonPayment') && (
                          <div className="absolute right-3 top-3 text-netcare-gold">
                            <Edit3 className="w-4 h-4" />
                          </div>
                        )}
                      </div>
                    ) : (
                      <p className="text-netcare-white font-semibold text-lg">{documentInfo.reasonsForNonPayment}</p>
                    )}
                  </div>
                </div>
              </CardContent>
              {editedFields.length > 0 && isEditing && (
                <CardFooter className="bg-netcare-gold/10 border-t border-netcare-gold/20 py-3 px-6">
                  <div className="flex items-center text-sm text-netcare-gold">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    <span>{editedFields.length} field{editedFields.length !== 1 ? 's' : ''} edited. Click Save Changes to apply.</span>
                  </div>
                </CardFooter>
              )}
            </Card>

            {/* Action Buttons */}
            <div className="space-y-6">
              {/* Help Card */}
              <Card className="bg-netcare-dark-blue/40 border-netcare-cyan/30 overflow-hidden">
                <div className="h-1 bg-gradient-to-r from-netcare-cyan/50 to-netcare-light-cyan/50" />
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-netcare-cyan/20 rounded-full">
                      <HelpCircle className="w-5 h-5 text-netcare-cyan" />
                    </div>
                    <div>
                      <h4 className="text-netcare-cyan font-medium mb-1">Need assistance?</h4>
                      <p className="text-netcare-white/80 text-sm">
                        Our team is available to help with any questions about your claim. 
                        <a href="#" className="text-netcare-cyan hover:underline ml-1">Contact support</a>
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  onClick={handleCancel}
                  className="flex-1 netcare-button-secondary group"
                >
                  <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
                  Back to Documents
                </Button>
                <Button
                  onClick={handleSubmitClaim}
                  disabled={isSubmitting || editedFields.length > 0 && isEditing}
                  className={`flex-1 ${editedFields.length > 0 && isEditing ? 'bg-gray-700/50 text-gray-400 cursor-not-allowed' : 'netcare-button'}`}
                >
                  {isSubmitting ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </div>
                  ) : (
                    <>
                      <CheckCircle className="w-5 h-5 mr-2" />
                      Submit Claim
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      
      {/* Content ends here */}
    </div>
  );
}