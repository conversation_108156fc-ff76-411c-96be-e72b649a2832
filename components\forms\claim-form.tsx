'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';

interface ClaimFormProps {
  onSubmit?: (data: any) => void;
}

export default function ClaimForm({ onSubmit }: ClaimFormProps) {
  return (
    <Card className="netcare-card">
      <CardHeader>
        <CardTitle className="text-netcare-white text-xl">
          Claim Form
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-netcare-white/70">Claim form component will be implemented here.</p>
        <div className="flex justify-end pt-4">
          <Button className="netcare-button" onClick={() => onSubmit?.({})}>
            Submit
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
