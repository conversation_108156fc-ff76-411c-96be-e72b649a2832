'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  ArrowLeft,
  FileText, 
  File, 
  Upload, 
  X, 
  CheckCircle,
  HelpCircle,
  Plus,
  User, 
  LogOut
} from 'lucide-react';
import { ThemeToggle } from '@/components/ui/theme-toggle';
// Note: MainLayout is already applied in the root layout.tsx

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
}

export default function UploadDocumentsPage() {
  const router = useRouter();
  const [isDragging, setIsDragging] = useState<'medical-aid' | 'provider' | null>(null);
  const [medicalAidFiles, setMedicalAidFiles] = useState<UploadedFile[]>([]);
  const [providerFiles, setProviderFiles] = useState<UploadedFile[]>([]);
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const medicalAidInputRef = useRef<HTMLInputElement>(null);
  const providerInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = (files: FileList | null, type: 'medical-aid' | 'provider') => {
    if (!files) return;
    
    // Process each file with simulated upload progress
    Array.from(files).forEach(file => {
      const fileId = Math.random().toString(36).substring(2, 9);
      const newFile = {
        id: fileId,
        name: file.name,
        size: file.size,
        type: file.type
      };
      
      // Add file immediately with 0% progress
      setUploadProgress(prev => ({ ...prev, [fileId]: 0 }));
      
      if (type === 'medical-aid') {
        setMedicalAidFiles(prev => [...prev, newFile]);
      } else {
        setProviderFiles(prev => [...prev, newFile]);
      }
      
      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.floor(Math.random() * 15) + 5; // Random progress between 5-20%
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          
          // Clear progress after a delay
          setTimeout(() => {
            setUploadProgress(prev => {
              const newProgress = { ...prev };
              delete newProgress[fileId];
              return newProgress;
            });
          }, 1000);
        }
        
        setUploadProgress(prev => ({ ...prev, [fileId]: progress }));
      }, 300);
    });
  };

  const removeFile = (fileId: string, type: 'medical-aid' | 'provider') => {
    if (type === 'medical-aid') {
      setMedicalAidFiles(prev => prev.filter(file => file.id !== fileId));
    } else {
      setProviderFiles(prev => prev.filter(file => file.id !== fileId));
    }
  };

  const handleDragOver = (e: React.DragEvent, type: 'medical-aid' | 'provider') => {
    e.preventDefault();
    setIsDragging(type);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(null);
  };

  const handleDrop = (e: React.DragEvent, type: 'medical-aid' | 'provider') => {
    e.preventDefault();
    setIsDragging(null);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileUpload(e.dataTransfer.files, type);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const canSubmit = medicalAidFiles.length > 0 || providerFiles.length > 0;

  const handleSubmit = () => {
    if (canSubmit) {
      setIsSubmitting(true);
      
      // Simulate processing delay
      setTimeout(() => {
        router.push('/review-claim');
      }, 1000);
    }
  };

  const UploadZone = ({ 
    type, 
    title, 
    description, 
    files, 
    inputRef 
  }: { 
    type: 'medical-aid' | 'provider';
    title: string;
    description: string;
    files: UploadedFile[];
    inputRef: React.RefObject<HTMLInputElement>;
  }) => {
    // Determine icon and accent colors based on type
    const isActive = isDragging === type;
    const iconColor = type === 'medical-aid' ? 'text-netcare-cyan' : 'text-netcare-light-cyan';
    const accentColor = type === 'medical-aid' ? 'netcare-cyan' : 'netcare-light-cyan';
    const accentBgLight = type === 'medical-aid' ? 'bg-netcare-cyan/10' : 'bg-netcare-light-cyan/10';
    const accentBgMedium = type === 'medical-aid' ? 'bg-netcare-cyan/20' : 'bg-netcare-light-cyan/20';
    const borderColor = isActive 
      ? `border-${accentColor}` 
      : 'border-netcare-gold/30 hover:border-netcare-gold/50';
    
    return (
      <div className="space-y-8">
        <div className="space-y-2">
          <h3 className="text-2xl font-bold text-netcare-white tracking-tight">{title}</h3>
          <p className="text-netcare-white/80 text-lg">{description}</p>
        </div>

        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${accentBgLight}`}>
              <FileText className={`w-5 h-5 ${iconColor}`} />
            </div>
            <h4 className="text-xl font-semibold text-netcare-white">Upload Document</h4>
          </div>
          
          <div
            className={`border-2 border-dashed rounded-2xl p-10 text-center transition-all duration-300 cursor-pointer group ${
              isActive
                ? `${accentBgLight} ${borderColor}`
                : `hover:${accentBgLight} ${borderColor}`
            }`}
            onDragOver={(e) => handleDragOver(e, type)}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, type)}
            onClick={() => inputRef.current?.click()}
          >
            <div className="flex flex-col items-center space-y-6">
              <div className={`p-6 rounded-full transition-all duration-300 transform group-hover:scale-110 ${
                isActive ? accentBgMedium : `bg-netcare-gold/10 group-hover:${accentBgMedium}`
              }`}>
                <Upload className={`w-10 h-10 transition-all duration-300 ${iconColor}`} />
              </div>
              <div className="space-y-3">
                <p className="text-xl font-semibold text-netcare-white group-hover:text-netcare-gold transition-colors duration-300">
                  Drag and drop your file here
                </p>
                <p className="text-netcare-white/80 text-lg">
                  or <span className={`font-medium ${iconColor} underline cursor-pointer`}>Browse</span> from your computer
                </p>
                <div className="pt-2">
                  <span className="inline-flex items-center px-3 py-1 rounded-full bg-white/10 text-sm text-netcare-white/70">
                    <span className="mr-1">•</span> PDF, JPG, or PNG up to 5MB
                  </span>
                </div>
              </div>
            </div>
          </div>

          <input
            ref={inputRef}
            type="file"
            multiple
            accept=".pdf,.jpg,.jpeg,.png"
            onChange={(e) => handleFileUpload(e.target.files, type)}
            className="hidden"
          />

          {/* Uploaded Files */}
          {files.length > 0 && (
            <div className="space-y-4 pt-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-1.5 rounded-lg ${accentBgLight}`}>
                    <CheckCircle className={`w-4 h-4 ${iconColor}`} />
                  </div>
                  <h5 className="font-semibold text-netcare-white text-lg">Uploaded Files</h5>
                </div>
                <span className="text-sm text-netcare-white/60">{files.length} file{files.length !== 1 ? 's' : ''}</span>
              </div>
              
              <div className="space-y-3">
                {files.map((file) => (
                  <div
                    key={file.id}
                    className={`flex items-center justify-between p-4 rounded-xl border transition-all duration-300 ${accentBgLight} border-${accentColor}/30 hover:shadow-lg relative overflow-hidden`}
                  >
                    {/* Progress bar overlay */}
                    {uploadProgress[file.id] !== undefined && (
                      <div className="absolute inset-0 bg-gradient-to-r from-netcare-cyan/20 to-netcare-light-cyan/20 z-0">
                        <div 
                          className="h-full bg-gradient-to-r from-netcare-cyan/30 to-netcare-light-cyan/30 transition-all duration-300" 
                          style={{ width: `${uploadProgress[file.id]}%` }}
                        />
                      </div>
                    )}
                    
                    <div className="flex items-center space-x-4 z-10 relative">
                      <div className={`p-3 rounded-lg ${accentBgMedium} flex items-center justify-center`}>
                        {uploadProgress[file.id] !== undefined ? (
                          <div className="relative w-6 h-6 flex items-center justify-center">
                            <svg className="w-6 h-6" viewBox="0 0 36 36">
                              <circle 
                                cx="18" 
                                cy="18" 
                                r="16" 
                                fill="none" 
                                className="stroke-current text-netcare-white/20" 
                                strokeWidth="2"
                              />
                              <circle 
                                cx="18" 
                                cy="18" 
                                r="16" 
                                fill="none" 
                                className={`stroke-current ${iconColor}`} 
                                strokeWidth="2"
                                strokeDasharray="100"
                                strokeDashoffset={100 - uploadProgress[file.id]}
                                transform="rotate(-90 18 18)"
                              />
                            </svg>
                            <span className="absolute text-xs font-medium text-netcare-white">
                              {uploadProgress[file.id]}%
                            </span>
                          </div>
                        ) : (
                          <File className={`w-6 h-6 ${iconColor}`} />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-netcare-white text-base">{file.name}</p>
                        <p className="text-sm text-netcare-white/70 mt-0.5">
                          {formatFileSize(file.size)}
                          {uploadProgress[file.id] !== undefined && (
                            <span className="ml-2 text-netcare-cyan">Uploading...</span>
                          )}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile(file.id, type);
                      }}
                      className="text-netcare-white/70 hover:text-red-400 hover:bg-red-400/10 rounded-full h-8 w-8 p-0 z-10 relative"
                      disabled={uploadProgress[file.id] !== undefined}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-5xl mx-auto px-6 py-12">
        {/* Progress Indicator */}
        <div className="mb-10">
          <div className="flex items-center justify-between max-w-md mx-auto">
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-netcare-gold flex items-center justify-center text-netcare-navy font-bold">1</div>
              <p className="mt-2 text-netcare-white/80 text-sm">Claim Type</p>
            </div>
            <div className="h-1 flex-1 bg-netcare-gold/30 mx-2"></div>
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-netcare-gold flex items-center justify-center text-netcare-navy font-bold">2</div>
              <p className="mt-2 text-netcare-white/80 text-sm">Documents</p>
            </div>
            <div className="h-1 flex-1 bg-netcare-white/20 mx-2"></div>
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-netcare-white/20 flex items-center justify-center text-netcare-white/60 font-bold">3</div>
              <p className="mt-2 text-netcare-white/60 text-sm">Review</p>
            </div>
          </div>
        </div>
        
        {/* Back Navigation */}
        <div className="mb-8">
          <Link 
            href="/new-claim" 
            className="inline-flex items-center text-netcare-white/80 hover:text-netcare-gold transition-colors group font-medium"
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
            Back to Claim Types
          </Link>
        </div>

        {/* Page Header */}
        <div className="mb-12 max-w-3xl">
          <h2 className="text-4xl font-bold text-netcare-gold mb-4 tracking-tight">Submit Required Statements</h2>
          <p className="text-netcare-white/90 text-xl leading-relaxed">
            Upload your Medical Aid and Provider statements to process your claim. We'll extract the relevant information automatically.
          </p>
        </div>
        {/* Upload Sections */}
        <div className="space-y-10">
          {/* Medical Aid Statement Upload */}
          <Card className="netcare-card border-netcare-cyan/20 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
            <div className="h-2 bg-gradient-to-r from-netcare-cyan to-netcare-light-cyan"></div>
            <CardContent className="p-10">
              <UploadZone
                type="medical-aid"
                title="Medical Aid Statement"
                description="Upload your Medical Aid Statement to process your claim"
                files={medicalAidFiles}
                inputRef={medicalAidInputRef}
              />
            </CardContent>
          </Card>

          {/* Medical Provider Statement Upload */}
          <Card className="netcare-card border-netcare-light-cyan/20 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
            <div className="h-2 bg-gradient-to-r from-netcare-light-cyan to-netcare-pale-cyan"></div>
            <CardContent className="p-10">
              <UploadZone
                type="provider"
                title="Medical Provider Statement"
                description="Upload your Medical Provider Statement to complete your claim"
                files={providerFiles}
                inputRef={providerInputRef}
              />
            </CardContent>
          </Card>
        </div>

        {/* Submit Button */}
        <div className="flex justify-center mt-16">
          <Button 
            onClick={handleSubmit}
            disabled={!canSubmit || isSubmitting}
            className={!canSubmit ? 'bg-gray-700/50 text-gray-400 cursor-not-allowed' : 'netcare-button'}
          >
            {!canSubmit ? (
              'Upload Documents to Continue'
            ) : isSubmitting ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </div>
            ) : (
              <>
                <CheckCircle className="w-6 h-6 mr-3" />
                Continue to Review
              </>
            )}
          </Button>
        </div>

        {/* Help Text */}
        {!canSubmit && (
          <div className="text-center mt-6">
            <p className="text-netcare-white/70 flex items-center justify-center">
              <span className="w-1.5 h-1.5 rounded-full bg-netcare-gold mr-2"></span>
              Please upload at least one document to continue
            </p>
          </div>
        )}
        
        {/* Help Card */}
        <div className="mt-16 bg-netcare-navy/50 border border-netcare-gold/20 rounded-xl p-6 flex items-start space-x-4">
          <div className="p-3 rounded-full bg-netcare-gold/10">
            <HelpCircle className="w-6 h-6 text-netcare-gold" />
          </div>
          <div>
            <h4 className="font-semibold text-netcare-white text-lg mb-2">Need Help?</h4>
            <p className="text-netcare-white/70">
              If you're having trouble uploading your documents or need assistance, please contact our support team at <span className="text-netcare-gold"><EMAIL></span> or call <span className="text-netcare-gold">0800 123 456</span>.
            </p>
          </div>
        </div>
    </div>
  );
}