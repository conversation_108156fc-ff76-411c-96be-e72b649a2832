import './globals.css';
import type { Metadata } from 'next';
import { Open_Sans } from 'next/font/google';
import { ReduxProvider } from '@/components/providers/redux-provider';
import { ClientBodyWrapper } from '@/components/client-body-wrapper';
import MainLayout from '@/components/layout/MainLayout';

const openSans = Open_Sans({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Netcare App',
  description: 'Professional healthcare management system',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={openSans.className} suppressHydrationWarning={true}>
        <ClientBodyWrapper>
          <ReduxProvider>
            <MainLayout>
              {children}
            </MainLayout>
          </ReduxProvider>
        </ClientBodyWrapper>
      </body>
    </html>
  );
}