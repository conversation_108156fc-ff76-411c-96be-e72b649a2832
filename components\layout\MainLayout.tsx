import React from 'react';
import Header from './Header';
import Footer from './Footer';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <div className="flex flex-col min-h-screen netcare-section-professional">
      {/* Netcare accent bar with gradient */}
      <div className="netcare-accent-bar"></div>
      
      {/* Floating shapes for visual interest */}
      <div className="netcare-floating-shapes">
        <div className="netcare-shape netcare-shape-1"></div>
        <div className="netcare-shape netcare-shape-2"></div>
        <div className="netcare-shape netcare-shape-3"></div>
      </div>
      
      <Header />
      <main className="flex-grow container mx-auto p-4 sm:p-6 lg:p-8 relative z-10">
        {children}
      </main>
      <Footer />
    </div>
  );
};

export default MainLayout;
